{"pages": ["pages/index/index", "pages/userCenter/userCenter", "pages/creatOrder/creatOrder", "pages/orderList/orderList", "pages/orderDetail/orderDetail", "pages/login/login", "pages/driverInformation/driverInformation", "pages/collectCarInfo/collectCarInfo", "pages/verification/verification", "pages/facialIdentification/facialIdentification", "pages/driverSettings/driverSettings"], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#FFFFFF", "backgroundColor": "#FFFFFF"}, "tabBar": {"color": "#bfbfbf", "selectedColor": "#0165FF", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "text": ""}, {"pagePath": "pages/userCenter/userCenter", "text": ""}]}, "darkmode": true, "plugins": {"chooseLocation": {"version": "1.0.10", "provider": "wx76a9a06e5b4e693e"}, "WechatSI": {"version": "0.0.7", "provider": "wx069ba97219f66d99"}}, "requiredBackgroundModes": ["location"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序定位"}}, "requiredPrivateInfos": ["getLocation", "onLocationChange", "<PERSON><PERSON><PERSON><PERSON>", "choosePoi", "chooseLocation", "startLocationUpdate", "startLocationUpdateBackground"], "usingComponents": {}}
<tm-app class="data-v-967b50c2" u-s="{{['d']}}" u-i="967b50c2-0" bind:__l="__l">
    <map hidden="{{!c}}" key="{{1}}" id="map" class="map data-v-967b50c2" longitude="{{d}}" latitude="{{e}}" polyline="{{f}}" markers="{{g}}" scale="12" enable-traffic="{{false}}" show-location="{{true}}" enable-poi="{{true}}" enable-3D="{{true}}">
        <cover-view bindtap="{{b}}" class="location data-v-967b50c2">
            <theme-icon wx:if="{{a}}" class="data-v-967b50c2" u-i="967b50c2-1,967b50c2-0" bind:__l="__l" u-p="{{a}}"></theme-icon>
        </cover-view>
    </map>
    <map hidden="{{!j}}" key="{{2}}" id="driveMap" class="map data-v-967b50c2" longitude="{{k}}" latitude="{{l}}" polyline="{{m}}" markers="{{n}}" scale="12" enable-traffic="{{false}}" show-location="{{true}}" enable-poi="{{true}}" enable-3D="{{true}}">
        <cover-view bindtap="{{i}}" class="location data-v-967b50c2">
            <theme-icon wx:if="{{h}}" class="data-v-967b50c2" u-i="967b50c2-2,967b50c2-0" bind:__l="__l" u-p="{{h}}"></theme-icon>
        </cover-view>
    </map>
    <view wx:if="{{o}}" class="location-panel data-v-967b50c2">
        <tm-sheet wx:if="{{A}}" class="data-v-967b50c2" u-s="{{['d']}}" u-i="967b50c2-3,967b50c2-0" bind:__l="__l" u-p="{{A}}">
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{r}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-4,967b50c2-3" bind:__l="__l" u-p="{{r}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            距离：{{p}}公里
                        </text>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            时间：{{q}}分钟
                        </text>
                    </view>
                    <view class="data-v-967b50c2" slot="right"></view>
                </tm-cell>
            </view>
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{y}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-5,967b50c2-3" bind:__l="__l" u-p="{{y}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:420rpx">
                            {{s}}
                        </text>
                    </view>
                    <view slot="right">
                        <uni-icons wx:if="{{v}}" bindclick="{{t}}" class="mr-10 data-v-967b50c2" u-i="967b50c2-6,967b50c2-5" bind:__l="__l" u-p="{{v}}"></uni-icons>
                        <uni-icons wx:if="{{x}}" class="data-v-967b50c2" bindclick="{{w}}" u-i="967b50c2-7,967b50c2-5" bind:__l="__l" u-p="{{x}}"></uni-icons>
                    </view>
                </tm-cell>
            </view>
            <loading-button wx:if="{{z}}" class="data-v-967b50c2" u-i="967b50c2-8,967b50c2-3" bind:__l="__l" u-p="{{z}}"></loading-button>
            <loading-button wx:if="{{zCancel}}" class="data-v-967b50c2" u-i="967b50c2-8-cancel,967b50c2-3" bind:__l="__l" u-p="{{zCancel}}" catch:tap="cancelOrderHandle"></loading-button>
        </tm-sheet>
    </view>
    <view wx:if="{{B}}" class="location-panel data-v-967b50c2">
        <tm-sheet wx:if="{{M}}" class="data-v-967b50c2" u-s="{{['d']}}" u-i="967b50c2-9,967b50c2-0" bind:__l="__l" u-p="{{M}}">
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{E}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-10,967b50c2-9" bind:__l="__l" u-p="{{E}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            距离：{{C}}公里
                        </text>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            时间：{{D}}分钟
                        </text>
                    </view>
                    <view class="data-v-967b50c2" slot="right"></view>
                </tm-cell>
            </view>
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{K}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-11,967b50c2-9" bind:__l="__l" u-p="{{K}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:420rpx">
                            {{F}}
                        </text>
                    </view>
                    <view slot="right">
                        <uni-icons wx:if="{{H}}" bindclick="{{G}}" class="mr-10 data-v-967b50c2" u-i="967b50c2-12,967b50c2-11" bind:__l="__l" u-p="{{H}}"></uni-icons>
                        <uni-icons wx:if="{{J}}" class="data-v-967b50c2" bindclick="{{I}}" u-i="967b50c2-13,967b50c2-11" bind:__l="__l" u-p="{{J}}"></uni-icons>
                    </view>
                </tm-cell>
            </view>
            <loading-button wx:if="{{L}}" class="data-v-967b50c2" u-i="967b50c2-14,967b50c2-9" bind:__l="__l" u-p="{{L}}"></loading-button>
            <loading-button wx:if="{{LCancel}}" class="data-v-967b50c2" u-i="967b50c2-14-cancel,967b50c2-9" bind:__l="__l" u-p="{{LCancel}}" catch:tap="cancelOrderHandle"></loading-button>
        </tm-sheet>
    </view>
    <view wx:if="{{N}}" class="location-panel data-v-967b50c2">
        <tm-sheet wx:if="{{Y}}" class="data-v-967b50c2" u-s="{{['d']}}" u-i="967b50c2-15,967b50c2-0" bind:__l="__l" u-p="{{Y}}">
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{Q}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-16,967b50c2-15" bind:__l="__l" u-p="{{Q}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            距离：{{O}}公里
                        </text>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            时间：{{P}}分钟
                        </text>
                    </view>
                    <view class="data-v-967b50c2" slot="right"></view>
                </tm-cell>
            </view>
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{W}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-17,967b50c2-15" bind:__l="__l" u-p="{{W}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:420rpx">
                            {{R}}
                        </text>
                    </view>
                    <view slot="right">
                        <uni-icons wx:if="{{T}}" bindclick="{{S}}" class="mr-10 data-v-967b50c2" u-i="967b50c2-18,967b50c2-17" bind:__l="__l" u-p="{{T}}"></uni-icons>
                        <uni-icons wx:if="{{V}}" class="data-v-967b50c2" bindclick="{{U}}" u-i="967b50c2-19,967b50c2-17" bind:__l="__l" u-p="{{V}}"></uni-icons>
                    </view>
                </tm-cell>
            </view>
            <loading-button wx:if="{{X}}" class="data-v-967b50c2" u-i="967b50c2-20,967b50c2-15" bind:__l="__l" u-p="{{X}}"></loading-button>
            <loading-button wx:if="{{XCancel}}" class="data-v-967b50c2" u-i="967b50c2-20-cancel,967b50c2-15" bind:__l="__l" u-p="{{XCancel}}" catch:tap="cancelOrderHandle"></loading-button>
        </tm-sheet>
    </view>
    <view wx:if="{{Z}}" class="location-panel data-v-967b50c2">
        <tm-sheet wx:if="{{ak}}" class="data-v-967b50c2" u-s="{{['d']}}" u-i="967b50c2-21,967b50c2-0" bind:__l="__l" u-p="{{ak}}">
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{ac}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-22,967b50c2-21" bind:__l="__l" u-p="{{ac}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            距离：{{aa}}公里
                        </text>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:300rpx">
                            时间：{{ab}}分钟
                        </text>
                    </view>
                    <view class="data-v-967b50c2" slot="right"></view>
                </tm-cell>
            </view>
            <view class="data-v-967b50c2">
                <tm-cell wx:if="{{ai}}" class="data-v-967b50c2" u-s="{{['title','right']}}" u-i="967b50c2-23,967b50c2-21" bind:__l="__l" u-p="{{ai}}">
                    <view class="flex flex-row flex-row-center-start data-v-967b50c2" slot="title">
                        <view class="data-v-967b50c2" style="height:20rpx;width:20rpx;background-color:#48b6fc;border-radius:50%"></view>
                        <text class="ml-20 text-overflow-3 data-v-967b50c2" style="width:420rpx">
                            {{ad}}
                        </text>
                    </view>
                    <view slot="right">
                        <uni-icons wx:if="{{af}}" bindclick="{{ae}}" class="mr-10 data-v-967b50c2" u-i="967b50c2-24,967b50c2-23" bind:__l="__l" u-p="{{af}}"></uni-icons>
                        <uni-icons wx:if="{{ah}}" class="data-v-967b50c2" bindclick="{{ag}}" u-i="967b50c2-25,967b50c2-23" bind:__l="__l" u-p="{{ah}}"></uni-icons>
                    </view>
                </tm-cell>
            </view>
            <arrive-at-the-destination wx:if="{{aj}}" class="data-v-967b50c2" u-i="967b50c2-26,967b50c2-21" bind:__l="__l" u-p="{{aj}}"></arrive-at-the-destination>
        </tm-sheet>
    </view>
</tm-app>
.data-v-driver-settings {
  /* 页面样式 */
}

.mb-32.data-v-driver-settings {
  margin-bottom: 32rpx;
}

.mt-32.data-v-driver-settings {
  margin-top: 32rpx;
}

.mb-16.data-v-driver-settings {
  margin-bottom: 16rpx;
}

/* 开关样式 */
switch.data-v-driver-settings {
  transform: scale(1.2);
}

/* 表单行样式 */
.form-row.data-v-driver-settings {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label.data-v-driver-settings {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  min-width: 200rpx;
}

/* 输入框和单位容器 */
.input-with-unit.data-v-driver-settings {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

/* 短输入框样式 */
.short-input.data-v-driver-settings {
  width: 180rpx;
  height: 60rpx;
  padding: 0 12rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  background-color: #fff;
  text-align: center;
  margin-right: 16rpx;
}

.short-input.data-v-driver-settings:focus {
  border-color: #007aff;
}

/* placeholder样式 */
.short-input.data-v-driver-settings::placeholder {
  font-size: 22rpx;
  color: #999;
}

/* 单位文字样式 */
.unit-text.data-v-driver-settings {
  font-size: 28rpx;
  color: #666;
}

/* 开关容器样式 */
.switch-container.data-v-driver-settings {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

<tm-app class="data-v-driver-settings" u-s="{{['d']}}" u-i="driver-settings-0" bind:__l="__l">
  <tm-sheet class="data-v-driver-settings" u-i="driver-settings-1,driver-settings-0" bind:__l="__l" u-p="{{p}}">
    <tm-text class="mb-32 data-v-driver-settings" u-i="driver-settings-2,driver-settings-1" bind:__l="__l" u-p="{{a}}"></tm-text>

    <tm-form class="data-v-driver-settings" u-i="driver-settings-3,driver-settings-1" bind:__l="__l" u-p="{{k}}">
      <view class="form-row data-v-driver-settings">
        <text class="form-label data-v-driver-settings">订单里程设置</text>
        <view class="input-with-unit data-v-driver-settings">
          <input
            class="short-input data-v-driver-settings"
            placeholder="0为无限制"
            type="number"
            value="{{c}}"
            bindinput="{{b}}"
          />
          <text class="unit-text data-v-driver-settings">公里</text>
        </view>
      </view>

      <view class="form-row data-v-driver-settings">
        <text class="form-label data-v-driver-settings">接单里程设置</text>
        <view class="input-with-unit data-v-driver-settings">
          <input
            class="short-input data-v-driver-settings"
            placeholder="输入里程"
            type="number"
            value="{{f}}"
            bindinput="{{e}}"
          />
          <text class="unit-text data-v-driver-settings">公里</text>
        </view>
      </view>

      <view class="form-row data-v-driver-settings">
        <text class="form-label data-v-driver-settings">是否自动接单</text>
        <view class="switch-container data-v-driver-settings">
          <switch class="data-v-driver-settings" bindchange="{{h}}" checked="{{i}}"></switch>
        </view>
      </view>
    </tm-form>

    <view class="mt-32 data-v-driver-settings">
      <tm-button class="mb-16 data-v-driver-settings" u-i="driver-settings-10,driver-settings-1" bind:__l="__l" bindclick="{{l}}" u-p="{{m}}"></tm-button>
      <tm-button class="data-v-driver-settings" u-i="driver-settings-11,driver-settings-1" bind:__l="__l" bindclick="{{n}}" u-p="{{o}}"></tm-button>
    </view>
  </tm-sheet>
</tm-app>

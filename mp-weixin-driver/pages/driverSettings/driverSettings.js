"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user_index = require("../../api/user/index.js");

if (!Array) {
  const _easycom_tm_text2 = common_vendor.resolveComponent("tm-text");
  const _easycom_tm_input2 = common_vendor.resolveComponent("tm-input");
  const _easycom_tm_form_item2 = common_vendor.resolveComponent("tm-form-item");
  const _easycom_tm_form2 = common_vendor.resolveComponent("tm-form");
  const _easycom_tm_button2 = common_vendor.resolveComponent("tm-button");
  const _easycom_tm_sheet2 = common_vendor.resolveComponent("tm-sheet");
  const _easycom_tm_app2 = common_vendor.resolveComponent("tm-app");
  (_easycom_tm_text2 + _easycom_tm_input2 + _easycom_tm_form_item2 + _easycom_tm_form2 + _easycom_tm_button2 + _easycom_tm_sheet2 + _easycom_tm_app2)();
}

const _easycom_tm_text = () => "../../tmui/components/tm-text/tm-text.js";
const _easycom_tm_input = () => "../../tmui/components/tm-input/tm-input.js";
const _easycom_tm_form_item = () => "../../tmui/components/tm-form-item/tm-form-item.js";
const _easycom_tm_form = () => "../../tmui/components/tm-form/tm-form.js";
const _easycom_tm_button = () => "../../tmui/components/tm-button/tm-button.js";
const _easycom_tm_sheet = () => "../../tmui/components/tm-sheet/tm-sheet.js";
const _easycom_tm_app = () => "../../tmui/components/tm-app/tm-app.js";

if (!Math) {
  (_easycom_tm_text + _easycom_tm_input + _easycom_tm_form_item + _easycom_tm_form + _easycom_tm_button + _easycom_tm_sheet + _easycom_tm_app)();
}

const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "driverSettings",
  setup(__props) {
    const formData = common_vendor.ref({
      orderDistance: 0,
      acceptDistance: 5,
      isAutoAccept: 0
    });
    
    const loading = common_vendor.ref(false);
    
    // 页面加载时获取当前设置
    common_vendor.onLoad(() => {
      getDriverSettings();
    });
    
    // 获取司机设置
    async function getDriverSettings() {
      try {
        const res = await api_user_index.getDriverSet();
        if (res.code === 200 && res.data) {
          formData.value = {
            orderDistance: res.data.orderDistance !== null && res.data.orderDistance !== undefined ? Number(res.data.orderDistance) : 0,
            acceptDistance: res.data.acceptDistance !== null && res.data.acceptDistance !== undefined ? Number(res.data.acceptDistance) : 5,
            isAutoAccept: res.data.isAutoAccept !== null && res.data.isAutoAccept !== undefined ? res.data.isAutoAccept : 0
          };
          console.log("设置数据加载完成:", formData.value);
        }
      } catch (error) {
        console.error("获取司机设置失败:", error);
        common_vendor.index.showToast({
          title: "获取设置失败",
          icon: "none"
        });
      }
    }
    
    // 提交表单
    async function submitForm() {
      if (loading.value) return;
      
      // 验证表单
      if (formData.value.acceptDistance < 0) {
        common_vendor.index.showToast({
          title: "接单里程必须大于等于0",
          icon: "none"
        });
        return;
      }
      
      if (formData.value.orderDistance < 0) {
        common_vendor.index.showToast({
          title: "订单里程不能小于0",
          icon: "none"
        });
        return;
      }
      
      loading.value = true;

      console.log("提交的表单数据:", formData.value);
      console.log("订单里程:", formData.value.orderDistance, "类型:", typeof formData.value.orderDistance);
      console.log("接单里程:", formData.value.acceptDistance, "类型:", typeof formData.value.acceptDistance);
      console.log("自动接单:", formData.value.isAutoAccept, "类型:", typeof formData.value.isAutoAccept);

      try {
        const res = await api_user_index.updateDriverSet(formData.value);
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "设置保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          throw new Error(res.message || "保存失败");
        }
      } catch (error) {
        console.error("保存设置失败:", error);
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    }
    
    // 取消操作
    function cancelForm() {
      common_vendor.index.navigateBack();
    }
    
    // 订单里程输入变化
    function onOrderDistanceChange(e) {
      console.log("订单里程事件触发:", e);
      // 从 e.detail.value 获取实际值
      const value = e.detail.value;
      console.log("订单里程输入值:", value);
      formData.value.orderDistance = Number(value) || 0;
      console.log("更新后的formData.orderDistance:", formData.value.orderDistance);
    }

    // 接单里程输入变化
    function onAcceptDistanceChange(e) {
      console.log("接单里程事件触发:", e);
      // 从 e.detail.value 获取实际值
      const value = e.detail.value;
      console.log("接单里程输入值:", value);
      formData.value.acceptDistance = Number(value) || 0;
      console.log("更新后的formData.acceptDistance:", formData.value.acceptDistance);
    }

    // 自动接单开关变化
    function onAutoAcceptChange(e) {
      formData.value.isAutoAccept = e.detail.value ? 0 : 1; // 0=自动接单，1=不自动接单
    }
    
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          ["font-size"]: 32,
          label: "个性化接单设置",
          color: "primary"
        }),
        b: common_vendor.o(onOrderDistanceChange),
        c: formData.value.orderDistance !== null && formData.value.orderDistance !== undefined ? formData.value.orderDistance : 0,

        e: common_vendor.o(onAcceptDistanceChange),
        f: formData.value.acceptDistance !== null && formData.value.acceptDistance !== undefined ? formData.value.acceptDistance : 5,

        h: common_vendor.o(onAutoAcceptChange),
        i: (formData.value.isAutoAccept !== null && formData.value.isAutoAccept !== undefined ? formData.value.isAutoAccept : 0) === 0,

        k: common_vendor.p({
          margin: [32, 24]
        }),
        l: common_vendor.o(submitForm),
        m: common_vendor.p({
          block: true,
          size: "large",
          color: "primary",
          loading: loading.value,
          label: "保存设置"
        }),
        n: common_vendor.o(cancelForm),
        o: common_vendor.p({
          block: true,
          size: "large",
          color: "grey-4",
          label: "取消"
        }),
        p: common_vendor.p({
          margin: [32, 24],
          padding: [32, 24],
          round: 3
        })
      };
    };
  }
});

const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-driver-settings"], ["__file", "D:/work/daijia_work/web/mp-weixin-driver/src/pages/driverSettings/driverSettings.vue"]]);
wx.createPage(MiniProgramPage);

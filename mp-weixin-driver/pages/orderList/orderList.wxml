<tm-app u-s="{{['d']}}" u-i="66a042e0-0" bind:__l="__l"><tm-segtab wx:if="{{c}}" class="mb-15" bindchange="{{a}}" u-i="66a042e0-1,66a042e0-0" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"></tm-segtab><tm-scrolly wx:if="{{p}}" u-s="{{['d']}}" bindrefresh="{{l}}" bindbottom="{{m}}" u-i="66a042e0-2,66a042e0-0" bind:__l="__l" bindupdateModelValue="{{n}}" bindupdateBottomValue="{{o}}" u-p="{{p}}"><view wx:for="{{d}}" wx:for-item="item" wx:key="n"><tm-sheet wx:if="{{k}}" u-s="{{['d']}}" bindclick="{{item.l}}" u-i="{{item.m}}" bind:__l="__l" u-p="{{k}}"><tm-cell wx:if="{{e}}" u-s="{{['title','right']}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{e}}"><tm-text u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}" slot="title"></tm-text><text class="ml-10 text-gray-1" slot="right">{{item.c}}</text></tm-cell><view><tm-cell wx:if="{{g}}" u-s="{{['title','right']}}" u-i="{{item.f}}" bind:__l="__l" u-p="{{g}}"><view class="flex flex-row flex-row-center-start text-size-m" slot="title"><view style="height:20rpx;width:20rpx;background-color:#93da5f;border-radius:50%"></view><text class="ml-20 text-overflow-1">{{item.e}}</text></view><text class="ml-10 text-gray-1" slot="right">{{f}}</text></tm-cell><tm-cell wx:if="{{i}}" u-s="{{['title','right']}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{i}}"><view class="flex flex-row flex-row-center-start" slot="title"><view style="height:20rpx;width:20rpx;background-color:#48b6fc;border-radius:50%"></view><text class="ml-20 text-overflow-1">{{item.g}}</text></view><text class="ml-10 text-gray-1" slot="right">{{h}}</text></tm-cell></view><tm-cell wx:if="{{j}}" u-s="{{['title','right']}}" u-i="{{item.k}}" bind:__l="__l" u-p="{{j}}"><tm-text u-i="{{item.i}}" bind:__l="__l" u-p="{{item.j}}" slot="title"></tm-text><view slot="right"></view></tm-cell></tm-sheet></view></tm-scrolly></tm-app>
<view class="flex relative flex-col data-v-dcacb728" style="{{'height:' + z}}"><picker-view wx:if="{{a}}" class="data-v-dcacb728" value="{{t}}" bindchange="{{v}}" style="{{'height:' + w}}" mask-style="{{x}}" immediateChange="{{y}}" indicator-style="height:50px"><picker-view-column wx:if="{{b}}" class="data-v-dcacb728"><view wx:for="{{c}}" wx:for-item="item" wx:key="b" class="{{['flex', 'itemcel', 'flex-row', 'flex-row-center-center', 'data-v-dcacb728', item.c]}}"><text class="data-v-dcacb728" style="{{'color:' + d}}">{{item.a}}</text></view></picker-view-column><picker-view-column wx:if="{{e}}" class="data-v-dcacb728"><view wx:for="{{f}}" wx:for-item="item" wx:key="b" class="{{['flex', 'itemcel', 'flex-row', 'flex-row-center-center', 'data-v-dcacb728', item.c]}}"><text class="data-v-dcacb728" style="{{'color:' + g}}">{{item.a}}</text></view></picker-view-column><picker-view-column wx:if="{{h}}" class="data-v-dcacb728"><view wx:for="{{i}}" wx:for-item="item" wx:key="b" class="{{['flex', 'itemcel', 'flex-row', 'flex-row-center-center', 'data-v-dcacb728', item.c]}}"><text class="data-v-dcacb728" style="{{'color:' + j}}">{{item.a}}</text></view></picker-view-column><picker-view-column wx:if="{{k}}" class="data-v-dcacb728"><view wx:for="{{l}}" wx:for-item="item" wx:key="b" class="{{['flex', 'itemcel', 'flex-row', 'flex-row-center-center', 'data-v-dcacb728', item.c]}}"><text class="data-v-dcacb728" style="{{'color:' + m}}">{{item.a}}</text></view></picker-view-column><picker-view-column wx:if="{{n}}" class="data-v-dcacb728"><view wx:for="{{o}}" wx:for-item="item" wx:key="b" class="{{['flex', 'itemcel', 'flex-row', 'flex-row-center-center', 'data-v-dcacb728', item.c]}}"><text class="data-v-dcacb728" style="{{'color:' + p}}">{{item.a}}</text></view></picker-view-column><picker-view-column wx:if="{{q}}" class="data-v-dcacb728"><view wx:for="{{r}}" wx:for-item="item" wx:key="b" class="{{['flex', 'itemcel', 'flex-row', 'flex-row-center-center', 'data-v-dcacb728', item.c]}}"><text class="data-v-dcacb728" style="{{'color:' + s}}">{{item.a}}</text></view></picker-view-column></picker-view></view>
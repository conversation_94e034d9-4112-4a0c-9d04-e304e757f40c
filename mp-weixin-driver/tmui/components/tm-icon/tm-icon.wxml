<view render-whole="{{true}}" class="flex flex-row flex-row-center-center data-v-e3e455a0" style="{{x}}"><text wx:if="{{a}}" bindtap="{{b}}" bindlongpress="{{c}}" class="{{['data-v-e3e455a0', d, e, 'text-size-n d-inline-block', f, g, h]}}" style="{{i + ';' + j + ';' + k + ';' + l}}"></text><image wx:if="{{m}}" render-whole="{{true}}" bindtap="{{n}}" bindlongpress="{{o}}" ref="icon" src="{{p}}" class="{{['data-v-e3e455a0', q, r, s]}}" style="{{t + ';' + v + ';' + w}}"></image></view>
<view class="flex flex-col flex-col-top-start"><view class="flex flex-row flex-row-top-start relative" style="{{'flex-wrap:wrap' + ';' + ('width:' + k)}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="K" class="ma-5" style="{{'width:' + e}}"><tm-sheet wx:if="{{d}}" u-s="{{['d']}}" class="" u-i="{{item.J}}" bind:__l="__l" u-p="{{d}}"><tm-image wx:if="{{item.I}}" u-s="{{['extra']}}" binddelete="{{item.G}}" u-i="{{item.H}}" bind:__l="__l" u-p="{{item.I}}"><view slot="extra" class="relative flex-1 flex flex-col flex-col-bottom-start"><view wx:if="{{b}}" style="{{'background:' + 'rgba(0, 0, 0, 0.5)' + ';' + ('width:' + item.s)}}" class="{{[item.t, 'py-4', 'px-4', 'flex', 'flex-row', 'flex-row-center-start']}}"><tm-icon wx:if="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></tm-icon><tm-text wx:if="{{item.d}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"></tm-text><tm-icon wx:if="{{item.g}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"></tm-icon><tm-text wx:if="{{item.j}}" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"></tm-text><tm-icon wx:if="{{item.m}}" u-i="{{item.n}}" bind:__l="__l" u-p="{{item.o}}"></tm-icon><tm-text wx:if="{{item.p}}" u-i="{{item.q}}" bind:__l="__l" u-p="{{item.r}}"></tm-text></view><view wx:if="{{c}}" class="absolute l-0 t-0 flex flex-row flex-row-center-between flex-1" style="{{'width:' + item.D + ';' + ('height:' + item.E) + ';' + ('top:' + item.F)}}"><view catchtap="{{item.x}}" class="{{[item.y, 'flex', 'flex-row', 'flex-row-center-center', 'px-12', 'py-4', 'round-tr-12', 'round-br-12']}}" style="{{'background:' + 'rgba(0, 0, 0, 0.5)'}}"><tm-icon wx:if="{{item.w}}" u-i="{{item.v}}" bind:__l="__l" u-p="{{item.w}}"></tm-icon></view><view catchtap="{{item.B}}" class="{{[item.C, 'flex', 'flex-row', 'flex-row-center-center', 'px-12', 'py-4', 'round-tl-12', 'round-bl-12']}}" style="{{'background:' + 'rgba(0, 0, 0, 0.5)'}}"><tm-icon wx:if="{{item.A}}" u-i="{{item.z}}" bind:__l="__l" u-p="{{item.A}}"></tm-icon></view></view></view></tm-image></tm-sheet></view><view wx:if="{{f}}" bindtap="{{i}}" class="ma-5" style="{{'width:' + j}}"><tm-sheet wx:if="{{h}}" u-s="{{['d']}}" u-i="943ed194-10" bind:__l="__l" u-p="{{h}}"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><tm-icon wx:if="{{g}}" u-i="943ed194-11,943ed194-10" bind:__l="__l" u-p="{{g}}"></tm-icon></block></tm-sheet></view></view></view>
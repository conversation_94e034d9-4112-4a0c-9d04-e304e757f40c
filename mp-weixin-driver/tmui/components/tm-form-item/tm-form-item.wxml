<view class="{{[B]}}"><tm-sheet wx:if="{{y}}" u-s="{{['d']}}" u-i="5a89d8ec-0" bind:__l="__l" u-p="{{y}}"><view class="{{[x]}}"><view wx:if="{{a}}" style="{{'height:' + b}}"></view><view class="{{['flex', k, l, m, n, o]}}"><view wx:if="{{c}}" style="{{g}}" class="{{['flex', 'flex-row', h, i]}}"><view style="width:12px" class="flex flex-row flex-row-center-center"><tm-text wx:if="{{d}}" u-i="5a89d8ec-1,5a89d8ec-0" bind:__l="__l" u-p="{{e}}"></tm-text></view><view class="flex flex-1" style="width:0px"><tm-text wx:if="{{f}}" u-i="5a89d8ec-2,5a89d8ec-0" bind:__l="__l" u-p="{{f}}"></tm-text></view></view><view class="flex-1" style="{{j}}"><slot></slot></view></view><view class="{{q}}"><block wx:if="{{$slots.desc}}"><slot name="desc"></slot></block><block wx:else><tm-text wx:if="{{p}}" u-i="5a89d8ec-3,5a89d8ec-0" bind:__l="__l" u-p="{{p}}"></tm-text></block></view><view wx:if="{{r}}" style="{{'height:' + w}}"><view wx:if="{{s}}"><block wx:if="{{$slots.error}}"><slot name="error"></slot></block><block wx:else><tm-text wx:if="{{t}}" u-i="5a89d8ec-4,5a89d8ec-0" bind:__l="__l" u-p="{{t}}"></tm-text></block></view></view></view></tm-sheet><view wx:if="{{z}}"><tm-divider wx:if="{{A}}" u-i="5a89d8ec-5" bind:__l="__l" u-p="{{A}}"></tm-divider></view></view>
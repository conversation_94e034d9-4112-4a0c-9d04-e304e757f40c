"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../../common/vendor.js");
const share = (args = {}) => {
  let defaultWxshareConfig = __spreadValues({}, args);
  let shareAppOptions = {};
  let shareTimeOptions = {};
  const shareApp = (options = {}) => {
    common_vendor.onShareAppMessage((res) => {
      return __spreadValues(__spreadValues(__spreadValues({}, defaultWxshareConfig), options), shareAppOptions);
    });
  };
  const setShareApp = (options = {}) => {
    shareAppOptions = options;
  };
  const shareTime = (options = {}) => {
    common_vendor.onShareTimeline(() => {
      return __spreadValues(__spreadValues(__spreadValues({}, defaultWxshareConfig), options), shareTimeOptions);
    });
  };
  const setShareTime = (options = {}) => {
    shareTimeOptions = options;
  };
  return {
    onShareAppMessage: shareApp,
    onShareTimeline: shareTime,
    setShareApp,
    setShareTime
  };
};
exports.share = share;

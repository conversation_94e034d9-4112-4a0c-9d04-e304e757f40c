"use strict";
var SECONDS_A_MINUTE = 60;
var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
var MILLISECONDS_A_SECOND = 1e3;
var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND;
var MS = "millisecond";
var S = "second";
var MIN = "minute";
var H = "hour";
var D = "day";
var W = "week";
var M = "month";
var Q = "quarter";
var Y = "year";
var DATE = "date";
var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
var INVALID_DATE_STRING = "Invalid Date";
var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;
exports.D = D;
exports.DATE = DATE;
exports.FORMAT_DEFAULT = FORMAT_DEFAULT;
exports.H = H;
exports.INVALID_DATE_STRING = INVALID_DATE_STRING;
exports.M = M;
exports.MILLISECONDS_A_DAY = MILLISECONDS_A_DAY;
exports.MILLISECONDS_A_HOUR = MILLISECONDS_A_HOUR;
exports.MILLISECONDS_A_MINUTE = MILLISECONDS_A_MINUTE;
exports.MILLISECONDS_A_SECOND = MILLISECONDS_A_SECOND;
exports.MILLISECONDS_A_WEEK = MILLISECONDS_A_WEEK;
exports.MIN = MIN;
exports.MS = MS;
exports.Q = Q;
exports.REGEX_FORMAT = REGEX_FORMAT;
exports.REGEX_PARSE = REGEX_PARSE;
exports.S = S;
exports.W = W;
exports.Y = Y;

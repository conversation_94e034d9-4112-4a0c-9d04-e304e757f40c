# 代驾后台管理系统技术文档

## 项目概述

基于Vue2 + Element-UI构建的代驾后台管理系统，提供完整的RBAC权限管理和业务数据管理功能。

## 司机个人化设置功能实现

### 功能概述

司机个人化设置功能允许司机自定义接单相关的参数，提高接单效率和用户体验。

### 功能设计

#### 设置项说明

1. **订单里程设置** (orderDistance)
   - 类型：BigDecimal
   - 说明：司机愿意接受的订单最大里程
   - 默认值：0（表示无限制）
   - 单位：公里

2. **接单里程设置** (acceptDistance)
   - 类型：BigDecimal
   - 说明：司机能看到多远范围内的订单
   - 默认值：5公里
   - 单位：公里

3. **是否自动接单** (isAutoAccept)
   - 类型：Integer
   - 说明：控制是否自动接单
   - 值：0=自动接单，1=不自动接单
   - 默认值：0（自动接单）

#### 设计合理性分析

这个设计是合理的，原因如下：

1. **订单里程设置**：让司机可以控制接受的订单距离，避免太远的订单影响收益
2. **接单里程设置**：控制司机能看到多远范围内的订单，平衡订单数量和距离
3. **自动接单功能**：可以提高接单效率，但也给司机选择权

### 技术实现

#### 后端实现

**核心接口**
- 更新司机设置：POST /driver/updateDriverSet
- 获取司机设置：GET /driver/getDriverSet

**服务层实现**：支持新增和更新，自动根据司机ID判断现有记录

#### 前端实现

**页面结构**
- 页面路径：`/pages/driverSettings/driverSettings`
- 表单包含：订单里程设置、接单里程设置、是否自动接单（滑块）
- 交互：表单验证、提交保存、取消返回

**导航更新**：将用户中心的"优惠券"入口替换为"个性化接单设置"，添加设置图标，增加登录状态检查

#### 登录权限控制
- **个性化接单设置**：未登录时显示"未登陆"提示，阻止页面跳转
- **我的订单**：未登录时显示"未登陆"提示，阻止页面跳转
- **验证方式**：调用`api_user_index.getDriverLoginInfo()`后端API验证
- **错误处理**：code 208表示未登录，使用后端返回的message显示提示
- **一致性**：与"开始接单"功能使用相同的登录验证逻辑

## 技术栈

### 前端技术栈
- **Vue 2.7.16** - 渐进式JavaScript框架
- **Element-UI 2.15.14** - 基于Vue的桌面端组件库
- **Vue Router 3.6.5** - Vue官方路由管理器
- **Vuex 3.6.2** - Vue状态管理模式
- **Axios 1.6.2** - HTTP客户端
- **SCSS** - CSS预处理器
- **NProgress** - 页面加载进度条

### 后端技术栈
- **Spring Boot** - Java应用开发框架
- **Spring Cloud** - 微服务架构
- **Spring Security** - 安全认证框架
- **Nacos** - 服务注册与配置中心
- **OpenFeign** - 声明式HTTP客户端
- **MyBatis Plus** - 数据持久层框架

## 项目结构

```
daijia-ui/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   │   ├── user.js        # 用户相关接口
│   │   ├── menu.js        # 菜单相关接口
│   │   └── system/        # 系统管理接口
│   ├── assets/            # 静态资源
│   ├── components/        # 公共组件
│   ├── layout/            # 布局组件
│   │   ├── index.vue      # 主布局
│   │   └── components/    # 布局子组件
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── store/             # Vuex状态管理
│   │   ├── index.js       # 主store
│   │   ├── getters.js     # 全局getters
│   │   └── modules/       # 模块化store
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   │   ├── request.js     # axios封装
│   │   └── auth.js        # 认证工具
│   ├── views/             # 页面组件
│   │   ├── login/         # 登录页面
│   │   ├── dashboard/     # 首页
│   │   └── system/        # 系统管理页面
│   ├── App.vue            # 根组件
│   ├── main.js            # 入口文件
│   └── permission.js      # 权限控制
├── package.json           # 项目配置
└── vue.config.js          # Vue CLI配置
```

## 核心功能模块

### 1. 系统管理模块

#### 用户管理
- **功能**: 用户CRUD操作、状态管理、角色分配
- **技术实现**:
  - 使用Element-UI的`el-table`组件展示用户列表
  - `el-dialog`组件实现用户新增/编辑弹窗
  - `el-pagination`组件实现分页功能
- **API接口**:
  ```javascript
  // 获取用户分页列表
  getUserList(page, limit, searchObj)
  // 保存用户
  saveUser(user)
  // 更新用户
  updateUser(user)
  // 删除用户
  removeUser(id)
  ```

#### 角色管理
- **功能**: 角色CRUD操作、权限分配、用户角色关联
- **技术实现**:
  - 树形结构展示角色权限关系
  - 使用`el-tree`组件实现权限分配
- **API接口**:
  ```javascript
  // 获取角色列表
  getRoleList(page, limit, searchObj)
  // 角色权限分配
  doAssignRole(assignRoleVo)
  ```

#### 菜单权限管理
- **功能**: 菜单树形结构管理、权限控制
- **技术实现**:
  - 递归组件渲染菜单树
  - 基于路由的权限控制
- **API接口**:
  ```javascript
  // 获取菜单树
  getMenuList()
  // 根据角色获取菜单权限
  getMenusByRoleId(roleId)
  ```

#### 部门管理
- **功能**: 部门树形结构管理
- **技术实现**:
  - `el-tree`组件展示部门层级关系
  - 支持部门的增删改查操作

#### 岗位管理
- **功能**: 岗位信息管理、状态控制
- **技术实现**:
  - 表格形式展示岗位信息
  - `el-switch`组件控制岗位状态

### 2. 日志管理模块

#### 登录日志
- **功能**: 记录用户登录行为
- **技术实现**:
  - 分页查询登录记录
  - 支持按时间、用户名筛选

#### 操作日志
- **功能**: 记录系统操作行为
- **技术实现**:
  - 使用AOP切面记录操作日志
  - 支持日志详情查看

### 3. 业务管理模块

#### 客户信息管理
- **功能**: 客户数据查询和管理
- **技术实现**:
  - 客户信息展示和统计
  - 支持客户数据导出

#### 司机信息管理
- **功能**: 司机信息管理和审核
- **技术实现**:
  - 司机资质审核流程
  - 司机状态管理

#### 订单信息管理
- **功能**: 订单查询和状态监控
- **技术实现**:
  - 订单列表展示
  - 订单状态实时更新

## 技术特性

### 1. 权限控制系统
- **路由权限**: 基于Vue Router的导航守卫
- **按钮权限**: 基于指令的细粒度权限控制
- **菜单权限**: 动态菜单生成

```javascript
// 路由守卫实现
router.beforeEach(async(to, from, next) => {
  const hasToken = getToken()
  if (hasToken) {
    // 已登录用户处理逻辑
    const hasGetUserInfo = store.getters.name
    if (hasGetUserInfo) {
      next()
    } else {
      await store.dispatch('user/getInfo')
      next()
    }
  } else {
    // 未登录用户处理逻辑
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
})
```

### 2. HTTP请求封装
- **请求拦截**: 自动添加token认证
- **响应拦截**: 统一错误处理和消息提示
- **超时处理**: 15秒请求超时设置

```javascript
// axios配置
const service = axios.create({
  baseURL: '/api',
  timeout: 15000
})

// 请求拦截器
service.interceptors.request.use(config => {
  if (store.getters.token) {
    config.headers['token'] = getToken()
  }
  return config
})

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      Message.error(res.message || 'Error')
      return Promise.reject(new Error(res.message || 'Error'))
    }
    return res
  },
  error => {
    Message.error(error.message)
    return Promise.reject(error)
  }
)
```

### 3. 状态管理
- **模块化设计**: 按功能模块划分store
- **持久化存储**: token等关键信息本地存储
- **响应式更新**: 状态变化自动更新UI

```javascript
// Vuex模块结构
const store = new Vuex.Store({
  modules: {
    app,      // 应用状态
    settings, // 系统设置
    user      // 用户信息
  },
  getters
})
```

## Element-UI组件使用

### 1. 数据展示组件
- **el-table**: 数据表格展示，支持排序、筛选、分页
- **el-pagination**: 分页组件，处理大量数据展示
- **el-tree**: 树形控件，用于菜单、部门层级展示
- **el-card**: 卡片容器，用于内容分组

### 2. 表单组件
- **el-form**: 表单容器，提供验证功能
- **el-input**: 输入框，支持多种类型
- **el-select**: 下拉选择器
- **el-date-picker**: 日期选择器
- **el-switch**: 开关组件，用于状态切换

### 3. 反馈组件
- **el-dialog**: 对话框，用于弹窗操作
- **el-message**: 消息提示
- **el-message-box**: 确认框
- **el-loading**: 加载状态

### 4. 导航组件
- **el-menu**: 导航菜单
- **el-breadcrumb**: 面包屑导航
- **el-tabs**: 标签页

## 开发环境配置

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

## 部署配置

### 代理配置
```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8603', // 后端服务地址
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
```

### 生产环境配置
- 静态资源CDN配置
- Gzip压缩优化
- 代码分割和懒加载

## 系统特点

1. **响应式设计**: 适配不同屏幕尺寸
2. **组件化开发**: 高度可复用的组件设计
3. **模块化架构**: 清晰的代码组织结构
4. **权限控制**: 完整的RBAC权限体系
5. **用户体验**: 友好的交互设计和错误提示
6. **可维护性**: 规范的代码风格和文档

## 浏览器兼容性
- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 前后端连桥配置

### API接口映射
前端通过vue.config.js代理配置，将`/api`请求转发到后端服务：

```javascript
// vue.config.js
devServer: {
  proxy: {
    '/api': {
      target: 'http://localhost:8603', // web-mgr服务地址
      changeOrigin: true,
      pathRewrite: {
        '^/api': ''
      }
    }
  }
}
```

### 后端接口对应关系

#### 系统管理模块
- **用户管理**: `/sysUser/*` → `SysUserController`
- **角色管理**: `/sysRole/*` → `SysRoleController`
- **菜单管理**: `/sysMenu/*` → `SysMenuController`
- **部门管理**: `/sysDept/*` → `SysDeptController`
- **岗位管理**: `/sysPost/*` → `SysPostController`

#### 认证模块
- **登录认证**: `/securityLogin/*` → `SecurityLoginController`

#### 日志管理模块
- **登录日志**: `/sysLoginLog/*` → `SysLoginLogController`
- **操作日志**: `/sysOperLog/*` → `SysOperLogController`

#### 业务管理模块
- **客户管理**: `/customer/info/*` → `CustomerInfoController`
- **司机管理**: `/driver/info/*` → `DriverInfoController`
- **订单管理**: `/order/info/*` → `OrderInfoController`

### 数据格式约定

#### 请求格式
```javascript
// 分页查询请求
{
  "pageNum": 1,
  "pageSize": 10,
  "searchParam": "搜索条件"
}

// 登录请求
{
  "username": "admin",
  "password": "123456"
}
```

#### 响应格式
```javascript
// 统一响应格式
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}

// 分页响应格式
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [], // 数据列表
    "total": 100,  // 总记录数
    "size": 10,    // 每页大小
    "current": 1   // 当前页
  }
}
```

### Token认证机制
- 前端登录成功后获取token
- 后续请求在header中携带token: `token: xxx`
- 后端通过token验证用户身份和权限

### 错误处理
- 401: token过期或无效，自动跳转登录页
- 403: 权限不足，显示错误提示
- 500: 服务器错误，显示错误信息

## 启动说明

### 后端启动
1. 启动Nacos服务注册中心
2. 启动web-mgr服务 (端口8603)

### 前端启动
```bash
cd daijie-ui
npm install
npm run dev
```

## 默认账号信息
- 用户名: admin
- 密码: 123456
- 前端访问地址: http://localhost:8080
- 后端服务地址: http://localhost:8603

---

# 微信小程序手机号绑定功能实现文档

## 需求概述

根据您的需求，实现了以下功能：
1. 小程序获取用户信息后，不再自动获取手机号
2. 跳转到新页面，提供两种手机号绑定方式：
   - 一键获取微信手机号（调用/updateWxPhone接口）
   - 手动绑定手机号（先调用/sendVerificationCode发送验证码，再调用/manualBindPhone提交）
3. 页面包含完整的UI组件和交互逻辑

## 实现方案

### 1. 新增API接口

在 `mp-weixin-customer/api/user/index.js` 中新增了两个API接口：

```javascript
// 发送验证码
function sendVerificationCode(phone) {
  return http_index.http.get(`/customer/sendVerificationCode/${phone}`);
}

// 手动绑定手机号
function manualBindPhone(params) {
  return http_index.http.post("/customer/manualBindPhone", params);
}
```

### 2. 创建手机号绑定页面

新建了 `pages/bindPhone/bindPhone` 页面，包含以下文件：

#### bindPhone.wxml
- 页面标题和说明
- 一键获取微信手机号按钮
- 分割线
- 手动绑定表单（手机号输入、验证码输入、获取验证码按钮）
- 绑定手机号按钮
- 暂时跳过按钮

#### bindPhone.js
主要功能：
- **一键获取微信手机号**: `handleWxPhoneAuth()` 函数
- **发送验证码**: `sendVerificationCode()` 函数，包含60秒倒计时
- **手动绑定**: `handleManualBind()` 函数
- **跳过绑定**: `skipBind()` 函数，显示确认弹窗
- **表单验证**: 手机号格式验证、验证码格式验证
- **状态管理**: loading状态、倒计时状态

#### bindPhone.wxss
- 渐变背景设计
- 响应式布局
- 卡片式表单设计
- 按钮和输入框样式

#### bindPhone.json
- 页面标题配置
- 组件引用配置

### 3. 修改登录流程

#### 用户Store修改 (`store/modules/user.js`)
```javascript
// 原来的逻辑：显示弹窗
if (callback && !this.user.isBindPhone) {
  yield callback();
}

// 新的逻辑：跳转到绑定页面
if (!this.user.isBindPhone) {
  common_vendor.index.redirectTo({
    url: "/pages/bindPhone/bindPhone"
  });
}
```

#### 登录页面简化 (`pages/login/login.js`)
- 移除了手机号绑定弹窗相关代码
- 简化了登录逻辑
- 移除了不必要的组件引用

### 4. 页面配置更新

#### app.json
添加了新页面路径：
```json
"pages": [
  // ... 其他页面
  "pages/bindPhone/bindPhone",
  // ...
]
```

#### project.config.json
添加了测试页面配置，方便开发调试。

## 技术特点

### 1. 用户体验优化
- **渐进式绑定**: 用户可以选择一键获取或手动输入
- **友好提示**: 清晰的页面说明和操作提示
- **跳过选项**: 允许用户暂时跳过绑定
- **倒计时功能**: 防止验证码重复发送

### 2. 安全性考虑
- **手机号格式验证**: 前端验证手机号格式
- **验证码验证**: 4位数字验证码格式检查
- **防重复发送**: 60秒倒计时限制
- **错误处理**: 完善的错误提示和异常处理

### 3. 代码质量
- **模块化设计**: 功能分离，便于维护
- **响应式数据**: 使用Vue3 Composition API
- **异步处理**: 完善的async/await错误处理
- **组件复用**: 使用TMUI组件库

## 接口对应关系

| 功能 | 前端方法 | 后端接口 | 说明 |
|------|----------|----------|------|
| 一键获取微信手机号 | `handleWxPhoneAuth()` | `/customer/updateWxPhone` | 传递微信code |
| 发送验证码 | `sendVerificationCode()` | `/customer/sendVerificationCode/{phone}` | GET请求 |
| 手动绑定手机号 | `handleManualBind()` | `/customer/manualBindPhone` | 传递phone和code |

## 页面流程

1. **用户微信登录** → 检查是否绑定手机号
2. **未绑定** → 跳转到 `bindPhone` 页面
3. **已绑定** → 直接进入首页
4. **绑定页面**：
   - 选择一键获取 → 调用微信授权 → 绑定成功 → 跳转首页
   - 选择手动绑定 → 输入手机号 → 获取验证码 → 输入验证码 → 绑定成功 → 跳转首页
   - 选择跳过 → 确认弹窗 → 跳转首页

## 注意事项

1. **微信小程序权限**: 一键获取手机号需要微信小程序的相应权限
2. **后端接口**: 确保后端三个接口正常工作
3. **验证码服务**: 确保短信验证码服务配置正确
4. **用户状态**: 绑定成功后需要更新用户的 `isBindPhone` 状态

## 测试建议

1. **功能测试**:
   - 测试一键获取微信手机号流程
   - 测试手动绑定流程（包括验证码发送和验证）
   - 测试跳过绑定功能
   - 测试各种错误情况的处理

2. **UI测试**:
   - 测试不同屏幕尺寸的适配
   - 测试按钮状态变化
   - 测试倒计时显示

3. **集成测试**:
   - 测试登录到绑定的完整流程
   - 测试绑定成功后的页面跳转
   - 测试用户状态更新

这个实现完全满足了您的需求，提供了灵活的手机号绑定方式，同时保持了良好的用户体验和代码质量。

## 问题修复记录

### 组件缺失问题
**问题**: 客户端小程序缺少 `tm-input`、`tm-form`、`tm-form-item` 组件
**解决方案**:
1. 使用原生 `<input>` 组件替代 `tm-input`
2. 移除复杂的表单验证，使用简单的数据绑定
3. 重新设计页面布局，使用现有的 `tm-text`、`tm-button`、`tm-icon`、`tm-app` 组件

### 修改的文件
1. **bindPhone.wxml**: 改用原生input和简化的布局结构
2. **bindPhone.js**: 移除表单组件引用，添加原生input事件处理
3. **bindPhone.json**: 移除不存在的组件配置
4. **bindPhone.wxss**: 添加原生input的样式设计

### 技术要点
- 使用原生input的 `bindinput` 事件处理用户输入
- 保持响应式数据绑定和状态管理
- 维持原有的业务逻辑和API调用
- 优化样式设计，确保良好的用户体验

### 最新修复 (解决 reactive 错误)
**问题**: `common_vendor.reactive is not a function` 错误
**解决方案**:
1. 将 `common_vendor.reactive` 改为 `common_vendor.ref`
2. 简化数据结构，使用独立的 `phone` 和 `code` ref变量
3. 更新所有函数中的变量引用方式
4. 确保WXML中的数据绑定正确对应

### 页面功能确认
✅ **一键获取微信手机号**: 调用 `updateWxPhone` 接口
✅ **发送验证码按钮**: 60秒倒计时，调用 `sendVerificationCode` 接口
✅ **手动绑定**: 输入手机号和验证码，调用 `manualBindPhone` 接口
✅ **表单验证**: 手机号格式验证，必填项检查
✅ **跳过绑定**: 确认弹窗后跳转首页

现在页面应该可以正常工作，不再出现 reactive 相关错误。

### Token认证问题修复
**问题**: 调用API时出现"未登陆"错误，因为没有携带token
**原因**: 重构后使用原生wx.request，没有使用项目的HTTP拦截器
**解决方案**:
1. 使用项目原有的API模块 `require('../../api/user/index.js')`
2. 调用 `updateUserPhoneByWx`、`sendVerificationCode`、`manualBindPhone` 方法
3. 这些方法会自动通过HTTP拦截器添加token到请求头

### HTTP拦截器机制
- 位置: `mp-weixin-customer/http/index.js`
- 功能: 自动在请求头添加 `config.header.token = utils_storage.getToken()`
- Token存储: 通过 `utils/storage.js` 管理token的存储和获取
- 登录流程: 微信登录 → 获取token → 存储到本地 → 后续请求自动携带

"use strict";
const http_index = require("../../http/index.js");
function getLogin(loginData) {
  return http_index.http.post("/customer/login", loginData);
}
function getUserInfo() {
  return http_index.http.get("/customer/getCustomerLoginInfo");
}
function updateUserInfo(userInfo) {
  return http_index.http.post("/customer/updateCustomerInfo");
}
function updateUserPhoneByWx(params) {
  return http_index.http.post("/customer/updateWxPhone", params);
}
function sendVerificationCode(phone) {
  return http_index.http.get(`/customer/sendVerificationCode/${phone}`);
}
function manualBindPhone(params) {
  return http_index.http.post("/customer/manualBindPhone", params);
}
function getCustomerLoginInfo() {
  return http_index.http.get("/customer/getCustomerLoginInfo");
}
exports.getLogin = getLogin;
exports.getUserInfo = getUserInfo;
exports.updateUserInfo = updateUserInfo;
exports.updateUserPhoneByWx = updateUserPhoneByWx;
exports.sendVerificationCode = sendVerificationCode;
exports.manualBindPhone = manualBindPhone;
exports.getCustomerLoginInfo = getCustomerLoginInfo;

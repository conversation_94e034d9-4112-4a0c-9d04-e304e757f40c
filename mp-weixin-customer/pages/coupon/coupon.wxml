<tm-app u-s="{{['d']}}" u-i="2204acec-0" bind:__l="__l"><tm-segtab wx:if="{{b}}" class="mb-15" u-i="2204acec-1,2204acec-0" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"></tm-segtab><tm-scrolly wx:if="{{m}}" u-s="{{['d']}}" bindrefresh="{{i}}" bindbottom="{{j}}" u-i="2204acec-2,2204acec-0" bind:__l="__l" bindupdateModelValue="{{k}}" bindupdateBottomValue="{{l}}" u-p="{{m}}"><block wx:if="{{c}}"><view wx:for="{{d}}" wx:for-item="item" wx:key="f"><tm-coupon wx:if="{{item.e}}" u-s="{{['thumb','extra']}}" bindclick="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"><text slot="thumb"></text><tm-text u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}" slot="extra"></tm-text></tm-coupon></view></block><block wx:if="{{e}}"><view wx:for="{{f}}" wx:for-item="item" wx:key="f"><tm-coupon wx:if="{{item.e}}" u-s="{{['thumb','extra']}}" bindclick="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"><text slot="thumb"></text><tm-text u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}" slot="extra"></tm-text></tm-coupon></view></block><block wx:if="{{g}}"><view wx:for="{{h}}" wx:for-item="item" wx:key="f"><tm-coupon wx:if="{{item.e}}" u-s="{{['thumb','extra']}}" bindclick="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"><text slot="thumb"></text><tm-text u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}" slot="extra"></tm-text></tm-coupon></view></block></tm-scrolly><tabbar-nav u-i="2204acec-9,2204acec-0" bind:__l="__l"></tabbar-nav></tm-app>
"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const store_modules_user = require("../../store/modules/user.js");
const api_user_index = require("../../api/user/index.js");
require("../../utils/storage.js");
require("../../config/constant.js");
require("../../http/index.js");
require("../../http/type.js");
if (!Array) {
  const _easycom_loading_button2 = common_vendor.resolveComponent("loading-button");
  const _easycom_tm_app2 = common_vendor.resolveComponent("tm-app");
  (_easycom_loading_button2 + _easycom_tm_app2)();
}
const _easycom_loading_button = () => "../../components/loading-button/loading-button.js";
const _easycom_tm_app = () => "../../tmui/components/tm-app/tm-app.js";
if (!Math) {
  (_easycom_loading_button + _easycom_tm_app)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = store_modules_user.useUserStore();

    function loginHandle() {
      // 直接调用getUserProfile（同步触发）
      common_vendor.index.getUserProfile({
        desc: '用于完善会员资料',
        lang: 'zh_CN',
        success: (profileRes) => {
          console.log('获取用户授权成功:', profileRes);
          const { encryptedData, iv, userInfo } = profileRes;

          // 获取code
          common_vendor.index.login({
            provider: "weixin",
            success: (loginRes) => {
              console.log('微信登录成功，code:', loginRes.code);

              const loginData = {
                code: loginRes.code,
                encryptedData,
                iv,
                userInfo: {
                  avatarUrl: userInfo.avatarUrl,
                  nickName: userInfo.nickName,
                  gender: userInfo.gender,
                  city: userInfo.city,
                  province: userInfo.province,
                  country: userInfo.country,
                  language: userInfo.language
                }
              };

              console.log('=== 用户授权登录数据 ===');
              console.log('code:', loginRes.code);
              console.log('encryptedData长度:', encryptedData ? encryptedData.length : 0);
              console.log('iv长度:', iv ? iv.length : 0);
              console.log('明文用户信息:', userInfo);
              console.log('nickName:', userInfo.nickName);
              console.log('avatarUrl:', userInfo.avatarUrl);
              console.log('========================');

              userStore.loginWithWechat(loginData).catch(handleLoginError);
            },
            fail: handleLoginError
          });
        },
        fail: (err) => {
          console.error('用户拒绝授权:', err);

          // 拒绝授权时仅用code登录
          common_vendor.index.login({
            provider: "weixin",
            success: (loginRes) => {
              console.log('微信登录成功（无用户信息），code:', loginRes.code);

              const loginData = {
                code: loginRes.code,
                encryptedData: "",
                iv: "",
                userInfo: null
              };

              console.log('=== 用户拒绝授权，仅使用code登录 ===');
              console.log('code:', loginRes.code);
              console.log('========================');

              userStore.loginWithWechat(loginData).catch(handleLoginError);
            },
            fail: handleLoginError
          });
        }
      });
    }

    // 统一错误处理
    const handleLoginError = (err) => {
      console.error("登录异常:", err);
      common_vendor.index.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0,
        b: common_vendor.p({
          type: "success",
          ["click-fun"]: loginHandle,
          shadow: 0,
          size: "large",
          label: "微信登陆"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cdfe2409"], ["__file", "D:/work/daijia_work/web/mp-weixin-customer/src/pages/login/login.vue"]]);
wx.createPage(MiniProgramPage);

<view class="bind-phone-page">
  <view class="container">
    <!-- 页面标题 -->
    <view class="title-section">
      <view class="title-text">绑定手机号</view>
      <view class="subtitle-text">为了保障您的账户安全，请绑定手机号</view>
    </view>

    <!-- 一键获取微信手机号 -->
    <view class="wx-phone-section">
      <button class="wx-phone-btn" open-type="getPhoneNumber" bindgetphonenumber="handleWxPhoneAuth">
        🔵 一键获取微信手机号
      </button>
    </view>

    <!-- 分割线 -->
    <view class="divider-section">
      <view class="divider-line"></view>
      <view class="divider-text">或</view>
      <view class="divider-line"></view>
    </view>

    <!-- 手动绑定手机号 -->
    <view class="manual-bind-section">
      <!-- 手机号输入 -->
      <view class="input-group">
        <view class="input-label">手机号</view>
        <view class="input-wrapper">
          <view class="input-icon">📱</view>
          <input
            class="input-field"
            placeholder="请输入手机号"
            maxlength="11"
            type="number"
            value="{{phone}}"
            bindinput="onPhoneInput"
          />
        </view>
      </view>

      <!-- 验证码输入 -->
      <view class="input-group">
        <view class="input-label">验证码</view>
        <view class="code-input-wrapper">
          <view class="input-wrapper code-input">
            <view class="input-icon">🛡️</view>
            <input
              class="input-field"
              placeholder="请输入验证码"
              maxlength="4"
              type="number"
              value="{{code}}"
              bindinput="onCodeInput"
            />
          </view>
          <button class="send-code-btn" bindtap="sendVerificationCode">
            {{countdown > 0 ? countdown + 's' : '获取验证码'}}
          </button>
        </view>
      </view>

      <!-- 绑定按钮 -->
      <view class="bind-btn-section">
        <button class="bind-btn" bindtap="handleManualBind">绑定手机号</button>
      </view>
    </view>

    <!-- 跳过绑定 -->
    <view class="skip-section">
      <button class="skip-btn" bindtap="skipBind">暂时跳过</button>
    </view>
  </view>
</view>

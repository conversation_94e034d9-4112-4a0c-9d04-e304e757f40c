.bind-phone-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 标题区域 */
.title-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.title-text {
  font-size: 64rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 微信手机号区域 */
.wx-phone-section {
  margin-bottom: 60rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}

.wx-phone-btn {
  width: 600rpx;
  height: 88rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.3);
}

/* 分割线区域 */
.divider-section {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 60rpx;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 30rpx;
}

.divider-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 手动绑定区域 */
.manual-bind-section {
  width: 100%;
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 输入组 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 0 24rpx;
  height: 88rpx;
  border: 2rpx solid #e9ecef;
  transition: border-color 0.3s;
}

.input-wrapper:focus-within {
  border-color: #007aff;
  background: white;
}

.input-icon {
  margin-right: 16rpx;
  flex-shrink: 0;
  font-size: 32rpx;
}

.input-field {
  flex: 1;
  height: 100%;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.input-field::placeholder {
  color: #999;
}

/* 验证码输入区域 */
.code-input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  flex-shrink: 0;
  width: 180rpx;
  height: 88rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.send-code-btn:disabled {
  background: #ccc;
  color: #999;
}

/* 绑定按钮区域 */
.bind-btn-section {
  margin-top: 40rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}

.bind-btn {
  width: 600rpx;
  height: 88rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.3);
}

.bind-btn:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

/* 跳过区域 */
.skip-section {
  width: 100%;
  display: flex;
  justify-content: center;
}

.skip-btn {
  width: 600rpx;
  height: 88rpx;
  background: white;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 24rpx;
  font-size: 28rpx;
}

/* 表单项样式调整 */
.manual-bind-section .tm-form-item {
  margin-bottom: 30rpx;
}

.manual-bind-section .tm-form-item:last-child {
  margin-bottom: 0;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .container {
    padding: 40rpx 30rpx;
  }

  .manual-bind-section {
    padding: 30rpx;
  }

  .code-input-wrapper {
    gap: 15rpx;
  }
}

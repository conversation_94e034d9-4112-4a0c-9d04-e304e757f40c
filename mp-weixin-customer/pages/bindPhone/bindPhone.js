// bindPhone.js
const { updateUserPhoneByWx, sendVerificationCode, manualBindPhone } = require('../../api/user/index.js');

Page({
  data: {
    phone: "",
    code: "",
    countdown: 0,
    wxPhoneLoading: false,
    sendCodeLoading: false,
    manualBindLoading: false
  },

  onLoad() {
    console.log('bindPhone页面加载');
  },

  // 一键获取微信手机号
  async handleWxPhoneAuth(e) {
    if (!e.detail.code) {
      wx.showToast({
        title: '获取手机号失败',
        icon: 'error'
      });
      return;
    }

    this.setData({ wxPhoneLoading: true });
    try {
      const res = await updateUserPhoneByWx({ code: e.detail.code });
      if (res.data) {
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({ url: '/pages/index/index' });
        }, 1500);
      }
    } catch (error) {
      console.error('微信手机号绑定失败:', error);
      wx.showToast({
        title: '绑定失败，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ wxPhoneLoading: false });
    }
  },
  // 发送验证码
  async sendVerificationCode() {
    if (!/^1[3-9]\d{9}$/.test(this.data.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'error'
      });
      return;
    }

    this.setData({ sendCodeLoading: true });
    try {
      const res = await sendVerificationCode(this.data.phone);
      if (res.data) {
        wx.showToast({
          title: '验证码发送成功',
          icon: 'success'
        });
        this.startCountdown();
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ sendCodeLoading: false });
    }
  },

  // 开始倒计时
  startCountdown() {
    this.setData({ countdown: 60 });
    this.countdownTimer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      this.setData({ countdown });
      if (countdown <= 0) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    }, 1000);
  },
  // 手动绑定手机号
  async handleManualBind() {
    if (!this.data.phone || !this.data.code) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'error'
      });
      return;
    }

    this.setData({ manualBindLoading: true });
    try {
      const res = await manualBindPhone({
        phone: this.data.phone,
        code: this.data.code
      });
      if (res.data) {
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });
        setTimeout(() => {
          wx.switchTab({ url: '/pages/index/index' });
        }, 1500);
      }
    } catch (error) {
      console.error('手动绑定失败:', error);
      wx.showToast({
        title: '绑定失败，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ manualBindLoading: false });
    }
  },

  // 跳过绑定
  skipBind() {
    wx.showModal({
      title: '提示',
      content: '跳过绑定可能影响部分功能使用，确定要跳过吗？',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({ url: '/pages/index/index' });
        }
      }
    });
  },
  // 输入处理
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },

  onCodeInput(e) {
    this.setData({ code: e.detail.value });
  },



  onUnload() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  }
});

package com.atguigu.daijia.model.form.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @date 2025/7/11
 * @description 描述
 */
@Data
public class CustomerLoginFrom {
    @Schema(description = "微信code")
    private String code;
    @Schema(description = "微信数据")
    private String encryptedData;
    @Schema(description = "微信iv")
    private String iv;
    @Schema(description = "明文用户信息")
    private Object userInfo;
}

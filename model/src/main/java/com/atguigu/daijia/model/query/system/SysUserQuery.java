//
//
package com.atguigu.daijia.model.query.system;


import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户查询实体
 * </p>
 */
@Data
public class SysUserQuery implements Serializable {

	private static final long serialVersionUID = 1L;

	private String keyword;

	// 新增具体搜索字段
	private String username;
	private String phone;
	private Integer status;

	private String createTimeBegin;
	private String createTimeEnd;

	private Long roleId;
	private Long postId;
	private Long deptId;

}


server:
  port: 8600
feign:
  sentinel:
    enabled: true
spring:
  cloud:
    openfeign:
      lazy-attributes-resolution: true
      client:
        config:
          default:
            connectTimeout: 30000
            readTimeout: 30000
            loggerLevel: basic
    gateway:
      discovery:      #是否与服务发现组件进行结合，通过 serviceId(必须设置成大写) 转发到具体的服务实例。默认为false，设为true便开启通过服务中心的自动根据 serviceId 创建路由的功能。
        locator:      #路由访问方式：http://Gateway_HOST:Gateway_PORT/大写的serviceId/**，其中微服务应用名默认大写访问。
          enabled: true
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            # 允许请求中携带的头信息
            allowedHeaders: "*"
            # 运行跨域的请求方式
            allowedMethods: "*"
            # 跨域检测的有效期,单位s
            maxAge: 36000
      routes:
        # web 接口
        - id: web-customer
          uri: lb://web-customer
          predicates:
            - Path=/customer-api/**
          filters:
            - StripPrefix=1
        - id: web-driver
          uri: lb://web-driver
          predicates:
            - Path=/driver-api/**
          filters:
            - StripPrefix=1
        - id: web-mgr
          uri: lb://web-mgr
          predicates:
            - Path=/mgr-api/**
          filters:
            - StripPrefix=1
        - id: service-payment
          uri: lb://service-payment
          predicates:
            - Path=/payment/**

<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>API测试</h1>
    <button onclick="testGetUserMenuList()">测试获取用户菜单</button>
    <div id="result"></div>

    <script>
        async function testGetUserMenuList() {
            try {
                const response = await fetch('/api/sysMenu/getUserMenuList', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'token': '8828288d734c4f5497ffc02afc88a8b1', // 使用你的token
                        'userId': '1',
                        'username': 'admin'
                    }
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                console.log('API响应:', data);
            } catch (error) {
                console.error('API调用失败:', error);
                document.getElementById('result').innerHTML = '错误: ' + error.message;
            }
        }
    </script>
</body>
</html>

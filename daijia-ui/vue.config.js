const path = require('path')

module.exports = {
  transpileDependencies: [],
  lintOnSave: false,

  devServer: {
    port: 8080,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8603', // 直连web-mgr服务
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },

  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },

  chainWebpack: config => {
    // 移除预加载
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')
  }
}

{"name": "daijia-ui", "version": "1.0.0", "description": "基于Vue2 + Element-UI的代驾后台管理系统", "main": "src/main.js", "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^1.6.2", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^2.4.0", "screenfull": "^5.2.0", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-router": "^4.5.19", "@vue/cli-plugin-vuex": "^4.5.19", "@vue/cli-service": "^4.5.19", "babel-eslint": "^10.1.0", "babel-loader": "^10.0.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.69.5", "sass-loader": "^10.4.1", "vue-template-compiler": "^2.7.16"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
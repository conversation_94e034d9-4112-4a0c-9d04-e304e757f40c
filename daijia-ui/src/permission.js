import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getToken } from "@/utils/auth"; // get token from cookie
import getPageTitle from "@/utils/get-page-title";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ["/login"]; // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start();

  // set page title
  document.title = getPageTitle(to.meta.title);

  // determine whether the user has logged in
  const hasToken = getToken();

  console.log("beforeEach", to.path, "roles:", store.getters.roles);

  if (hasToken) {
    if (to.path === "/login") {
      // if is logged in, redirect to the home page
      next({ path: "/" });
      NProgress.done();
    } else {
      // 检查用户信息和动态路由
      const hasUserInfo = store.getters.name;
      const hasRoutes = store.getters.addRoutes && store.getters.addRoutes.length > 0;
      console.log("hasUserInfo:", hasUserInfo, "hasRoutes:", hasRoutes);

      if (hasUserInfo && hasRoutes) {
        next();
      } else if (hasUserInfo && !hasRoutes) {
        // 有用户信息但没有路由，生成动态路由
        try {
          console.log("生成动态路由");
          const accessRoutes = await store.dispatch("permission/generateRoutes");
          console.log("动态路由生成成功:", accessRoutes);

          if (accessRoutes && accessRoutes.length > 0) {
            router.addRoutes(accessRoutes);
          }

          next({ ...to, replace: true });
        } catch (error) {
          console.error("动态路由生成失败:", error);
          // 路由生成失败不影响基本功能，继续访问
          next();
        }
      } else {
        try {
          // get user info
          console.log("调用 user/getInfo");
          await store.dispatch("user/getInfo");
          console.log("用户信息获取成功");

          // 获取用户信息后生成动态路由
          console.log("生成动态路由");
          const accessRoutes = await store.dispatch("permission/generateRoutes");
          console.log("动态路由生成成功:", accessRoutes);

          if (accessRoutes && accessRoutes.length > 0) {
            router.addRoutes(accessRoutes);
          }

          next({ ...to, replace: true });
        } catch (error) {
          // remove token and go to login page to re-login
          console.error("用户信息获取失败:", error);
          console.error("错误详情:", error.response);

          // 检查是否是认证相关错误
          if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            await store.dispatch("user/resetToken");
            Message.error("登录已过期，请重新登录");
            next(`/login?redirect=${to.path}`);
          } else if (error.message && (
            error.message.includes('token') ||
            error.message.includes('登录') ||
            error.message.includes('认证') ||
            error.message.includes('没有权限') ||
            error.message.includes('权限不足')
          )) {
            // token相关错误或权限错误
            await store.dispatch("user/resetToken");
            Message.error("登录已过期，请重新登录");
            next(`/login?redirect=${to.path}`);
          } else {
            // 其他错误，可能是网络问题，允许继续访问
            console.log("网络或其他错误，允许继续访问");
            next();
          }
          NProgress.done();
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});

@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

// 表格操作按钮样式
.el-table .cell {
  .el-button--text {
    padding: 0;
    margin-right: 10px;
  }
}

// 分页样式
.pagination-container {
  background: #fff;
  padding: 32px 16px;
  text-align: right;
}

// 搜索表单样式
.el-form--inline .el-form-item {
  margin-right: 10px;
  margin-bottom: 15px;
}

// 操作按钮行样式
.mb8 {
  margin-bottom: 8px;
  position: relative;
}

// 状态标签样式
.el-tag {
  margin-right: 5px;
}

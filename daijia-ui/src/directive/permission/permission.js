import store from "@/store";

export default {
  inserted(el, binding, vnode) {
    const { value } = binding;
    const permissions = store.getters && store.getters.permissions;

    console.log('权限指令检查:', value, '用户权限:', permissions);

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value;

      // 如果permissions为空或undefined，默认显示元素
      if (!permissions || permissions.length === 0) {
        console.log('用户权限为空，默认显示元素');
        return;
      }

      const hasPermissions = permissions.some((permission) => {
        return permissionFlag.includes(permission);
      });

      console.log('权限检查结果:', hasPermissions);

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      console.error(
        `权限指令需要数组参数! 例如: v-hasPermission="['bnt.sysUser.add','bnt.sysUser.edit']"`
      );
    }
  },
};

<template>
  <div class="app-wrapper">
    <div class="sidebar-container">
      <el-menu
        :default-active="activeMenu"
        mode="vertical"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <!-- 首页固定菜单 -->
        <el-menu-item index="/dashboard">
          <i class="el-icon-s-home"></i>
          <span slot="title">首页</span>
        </el-menu-item>

        <!-- 动态菜单 -->
        <template v-for="route in permission_routes">
          <el-submenu
            v-if="
              route.children && route.children.length > 0 && route.type !== 2
            "
            :key="route.id || route.path"
            :index="route.path"
          >
            <template slot="title">
              <template v-if="route.meta && route.meta.icon">
                <i :class="getMenuIcon(route.meta.icon)"></i>
              </template>
              <span>{{ route.meta.title }}</span>
            </template>
            <el-menu-item
              v-for="child in route.children"
              v-if="child.type !== 2"
              :key="child.id || child.path"
              :index="child.path"
            >
              <template v-if="child.meta && child.meta.icon">
                <i :class="getMenuIcon(child.meta.icon)"></i>
              </template>
              {{
                (child.meta && child.meta.title) || child.name || "未命名子菜单"
              }}
            </el-menu-item>
          </el-submenu>

          <el-menu-item
            v-else-if="route.type !== 2 && route.path && route.path !== '/'"
            :key="route.id || route.path"
            :index="route.path"
          >
            <template v-if="route.meta && route.meta.icon">
              <i :class="getMenuIcon(route.meta.icon)"></i>
            </template>
            <span slot="title">{{
              (route.meta && route.meta.title) || route.name || "未命名菜单"
            }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>

    <div class="main-container">
      <div class="navbar">
        <navbar />
      </div>
      <div class="app-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import Navbar from "./components/Navbar";
import { mapGetters } from "vuex";
import { getRouters } from "@/api/menu";

export default {
  name: "Layout",
  components: {
    Navbar,
  },
  data() {
    return {
      permission_routes: [],
    };
  },
  computed: {
    ...mapGetters(['permissions']),
    activeMenu() {
      return this.$route.path;
    },
  },
  mounted() {
    this.getMenuList();
  },
  methods: {
    /** 获取菜单列表 */
    async getMenuList() {
      try {
        console.log("=== 前端开始获取菜单 ===");
        const response = await getRouters();
        console.log("后端返回的完整响应:", response);
        console.log("动态菜单数据:", response.data);

        if (response.data && response.data.routerList) {
          console.log("路由列表数量:", response.data.routerList.length);
          console.log("路由列表详情:", response.data.routerList);

          // 后端已经根据用户角色权限过滤了菜单，前端直接使用
          this.permission_routes = response.data.routerList;
          console.log("设置动态菜单:", this.permission_routes);
          console.log("设置的菜单数量:", this.permission_routes.length);
        } else {
          console.log("没有获取到路由数据");
          this.permission_routes = [];
        }
        console.log("=== 前端菜单获取完成 ===");
      } catch (error) {
        console.error("获取菜单失败:", error);
        // 如果获取失败，使用空数组
        this.permission_routes = [];
      }
    },



    getMenuIcon(icon) {
      if (!icon) return "el-icon-menu";

      // 处理不同的图标格式
      if (icon.startsWith("el-icon-")) {
        return icon;
      } else if (icon.startsWith("icon-")) {
        return icon;
      } else {
        // 默认图标映射
        const iconMap = {
          Setting: "el-icon-setting",
          UserFilled: "el-icon-user",
          Menu: "el-icon-menu",
          Document: "el-icon-document",
          DocumentRemove: "el-icon-document-remove",
          List: "el-icon-s-order",
          Film: "el-icon-film",
          Headset: "el-icon-headset",
          Grid: "el-icon-s-grid",
          DocumentCopy: "el-icon-document-copy",
          system: "el-icon-setting",
          user: "el-icon-user",
          peoples: "el-icon-s-custom",
          "tree-table": "el-icon-menu",
          tree: "el-icon-s-unfold",
          post: "el-icon-postcard",
        };
        return iconMap[icon] || "el-icon-menu";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;

  .sidebar-container {
    width: 210px;
    height: 100vh;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    background-color: #304156;
    transition: width 0.28s;

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
  }

  .main-container {
    min-height: 100vh;
    transition: margin-left 0.28s;
    margin-left: 210px;
    position: relative;

    .navbar {
      height: 50px;
      overflow: hidden;
      position: relative;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    }

    .app-main {
      min-height: calc(100vh - 50px);
      width: 100%;
      position: relative;
      overflow: hidden;
    }
  }
}
</style>

import axios from "axios";
import { MessageBox, Message } from "element-ui";
import store from "@/store";
import { getToken } from "@/utils/auth";

// 创建axios实例
const service = axios.create({
  baseURL: "/api", // 通过vue.config.js代理转发到后端
  timeout: 15000, // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送之前做一些处理
    if (store.getters.token) {
      // 让每个请求携带token，根据后端要求设置header名称
      config.headers["token"] = getToken();

      // 添加用户ID和用户名到请求头
      console.log("=== 请求拦截器调试 ===");
      console.log("store.getters.userId:", store.getters.userId);
      console.log("store.getters.name:", store.getters.name);

      // 获取用户ID，如果没有则使用超级管理员ID
      const userId = store.getters.userId || "1";
      config.headers["userId"] = userId.toString();

      // 获取用户名，如果没有则使用admin
      const username = store.getters.name || "admin";
      config.headers["username"] = "admin"; // 先设置默认值

      console.log("设置的userId:", userId);
      console.log("设置的username:", username);

      // 如果有用户信息，则使用实际用户信息
      if (username && username !== "管理员") {
        try {
          // 检查是否包含非ASCII字符
          if (/[^\x00-\x7F]/.test(username)) {
            // 只设置 base64 字段，不设置原始 username 字段，避免 header 出现中文
            config.headers["username"] = undefined;
            config.headers["username-base64"] = btoa(
              unescape(encodeURIComponent(username))
            );
            config.headers["username-encoding"] = "base64";
          } else {
            // 纯ASCII字符，直接使用
            config.headers["username"] = username;
          }
        } catch (error) {
          console.error("用户名编码失败:", error);
          config.headers["username"] = "admin";
        }
      }

      console.log("请求头信息:", {
        token: config.headers["token"],
        userId: config.headers["userId"],
        username: config.headers["username"],
        usernameBase64: config.headers["username-base64"],
      });
    }
    return config;
  },
  (error) => {
    // 请求错误处理
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 如果自定义代码不是200，则判断为错误
    if (res.code !== 200) {
      Message({
        message: res.message || "Error",
        type: "error",
        duration: 5 * 1000,
      });

      // 401: 未授权，token过期等
      if (res.code === 401) {
        // 清除token并跳转到登录页
        MessageBox.confirm(
          "您已被登出，可以取消继续留在该页面，或者重新登录",
          "确定登出",
          {
            confirmButtonText: "重新登录",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          store.dispatch("user/resetToken").then(() => {
            location.reload();
          });
        });
      }

      // 检查是否是权限相关错误（通常是token过期或无效）
      if (
        res.code === 403 ||
        (res.message &&
          (res.message.includes("没有权限") ||
            res.message.includes("权限不足") ||
            res.message.includes("token") ||
            res.message.includes("登录") ||
            res.message.includes("认证")))
      ) {
        console.log("检测到权限相关错误，清除token并跳转登录页");
        store.dispatch("user/resetToken").then(() => {
          window.location.href = "/login";
        });
        return Promise.reject(new Error("登录已过期，请重新登录"));
      }

      return Promise.reject(new Error(res.message || "Error"));
    } else {
      return res;
    }
  },
  (error) => {
    console.log("请求错误详情:", error); // for debug
    console.log("错误响应:", error.response); // for debug

    let message = error.message;

    // 处理HTTP状态码错误
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      console.log("HTTP状态码:", status);
      console.log("响应数据:", data);
      console.log("当前token:", getToken());

      console.log("HTTP错误处理 - 状态码:", status, "响应数据:", data);

      switch (status) {
        case 403:
          console.log("403错误 - 检查是否为认证问题");
          // 对于403错误，如果有token但被拒绝，很可能是token过期或无效
          if (getToken()) {
            console.log("有token但403错误，判定为token过期");
            message = "登录已过期，请重新登录";
            // 自动清除token并跳转到登录页
            store.dispatch("user/resetToken").then(() => {
              window.location.href = "/login";
            });
          } else {
            message = "访问被拒绝，请检查权限";
          }
          break;
        case 401:
          console.log("401错误 - 认证失败");
          message = "登录已过期，请重新登录";
          // 自动清除token并跳转到登录页
          store.dispatch("user/resetToken").then(() => {
            window.location.href = "/login";
          });
          break;
        case 404:
          message = "请求的资源不存在";
          break;
        case 500:
          message = "服务器内部错误";
          break;
        default:
          if (data && data.message) {
            message = data.message;
          }
      }
    }

    Message({
      message: message,
      type: "error",
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default service;

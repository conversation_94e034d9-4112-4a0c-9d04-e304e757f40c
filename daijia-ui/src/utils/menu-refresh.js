import store from "@/store";
import router from "@/router";
import { resetRouter } from "@/router";

/**
 * 刷新用户菜单
 * 重新获取用户菜单并重新加载路由
 */
export function refreshUserMenu() {
  // 重置路由
  resetRouter();

  // 重新获取用户菜单
  return store.dispatch("permission/generateRoutes").then((accessRoutes) => {
    // 动态添加可访问路由
    router.addRoutes(accessRoutes);

    // 强制刷新当前页面以应用新的路由
    const { fullPath } = router.currentRoute;
    router.replace({
      path: "/redirect" + fullPath,
    });
  });
}

import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },

  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },

  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("@/views/dashboard/index"),
        meta: { title: "首页", icon: "dashboard" },
      },
    ],
  },

  // 系统管理静态路由
  {
    path: "/system",
    component: Layout,
    redirect: "/system/user",
    name: "System",
    meta: { title: "系统管理", icon: "system" },
    children: [
      {
        path: "user",
        name: "User",
        component: () => import("@/views/system/user/index"),
        meta: { title: "用户管理", icon: "user" },
      },
      {
        path: "role",
        name: "Role",
        component: () => import("@/views/system/role/index"),
        meta: { title: "角色管理", icon: "peoples" },
      },
      {
        path: "menu",
        name: "Menu",
        component: () => import("@/views/system/menu/index"),
        meta: { title: "菜单管理", icon: "tree-table" },
      },
      {
        path: "dept",
        name: "Dept",
        component: () => import("@/views/system/dept/index"),
        meta: { title: "部门管理", icon: "tree" },
      },
      {
        path: "post",
        name: "Post",
        component: () => import("@/views/system/post/index"),
        meta: { title: "岗位管理", icon: "post" },
      },
    ],
  },



  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user permissions
 */
export const asyncRoutes = [];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;

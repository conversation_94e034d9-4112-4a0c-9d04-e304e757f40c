import { login, logout, getInfo } from "@/api/user";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { resetRouter } from "@/router";

const getDefaultState = () => {
  return {
    token: getToken(),
    id: null,
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
  };
};

const state = getDefaultState();

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState());
  },
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_ID: (state, id) => {
    state.id = id;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions;
  },
};

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    const { username, password } = userInfo;
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password })
        .then((response) => {
          const { data } = response;
          if (data && data.token) {
            commit("SET_TOKEN", data.token);
            setToken(data.token);
            resolve();
          } else {
            reject(new Error("登录失败，未获取到token"));
          }
        })
        .catch((error) => {
          console.error("登录错误:", error);
          reject(error);
        });
    });
  },

  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo()
        .then((response) => {
          const { data } = response;

          console.log("=== 用户信息获取成功 ===");
          console.log("后端返回的完整数据:", data);

          if (!data) {
            return reject("验证失败，请重新登录。");
          }

          // 根据后端实际返回的数据结构调整
          const id = data.id || 1;
          const name = data.name || data.username || data.nickName || "管理员";
          const avatar =
            data.avatar ||
            data.headUrl ||
            "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png";
          const roles = data.roles || data.roleList || ["admin"];
          const permissions = data.permissions || data.permissionList || data.userPermsList || [];

          // 如果roles是字符串，转换为数组
          const roleArray = Array.isArray(roles) ? roles : [roles];
          const permissionArray = Array.isArray(permissions) ? permissions : [];

          console.log("解析后的用户ID:", id);
          console.log("解析后的用户名:", name);
          console.log("用户权限列表:", permissionArray);

          // 验证返回的roles是否是一个非空数组
          if (roleArray && roleArray.length > 0) {
            commit("SET_ROLES", roleArray);
          } else {
            commit("SET_ROLES", ["ROLE_DEFAULT"]);
          }

          commit("SET_ID", id);
          commit("SET_PERMISSIONS", permissionArray);
          commit("SET_NAME", name);
          commit("SET_AVATAR", avatar);
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // 用户登出
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          removeToken(); // must remove token first
          resetRouter();
          commit("RESET_STATE");
          // 重置访问的路由
          dispatch("permission/resetRoutes", null, { root: true });
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // 移除token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken(); // must remove token first
      commit("RESET_STATE");
      resolve();
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};

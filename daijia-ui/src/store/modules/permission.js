import { asyncRoutes, constantRoutes } from "@/router";
import { getRouters } from "@/api/menu";
import Layout from "@/layout";

/**
 * 通过meta.perms判断是否与当前用户权限匹配
 * @param perms
 * @param route
 */
function hasPermission(perms, route) {
  if (route.meta && route.meta.perms) {
    return perms.some((perm) => route.meta.perms.includes(perm));
  } else {
    return true;
  }
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param routes asyncRoutes
 * @param perms
 */
export function filterAsyncRoutes(routes, perms) {
  const res = [];

  routes.forEach((route) => {
    const tmp = { ...route };
    if (hasPermission(perms, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, perms);
      }
      res.push(tmp);
    }
  });

  return res;
}

/**
 * 动态路由生成
 */
export function generateRoutes(routers) {
  return filterAsyncRouter(routers);
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap) {
  console.log("filterAsyncRouter 输入:", asyncRouterMap);
  console.log("输入类型:", Array.isArray(asyncRouterMap));

  if (!Array.isArray(asyncRouterMap)) {
    console.error("asyncRouterMap 不是数组:", asyncRouterMap);
    return [];
  }

  return asyncRouterMap.filter((route) => {
    console.log("处理路由:", route.path, "组件:", route.component);

    if (route.component) {
      // Layout组件特殊处理
      if (route.component === "Layout") {
        console.log("设置Layout组件:", route.path);
        route.component = Layout;
      } else if (typeof route.component === 'string' && route.component.trim() !== "") {
        // 只有当组件路径不为空时才加载组件
        try {
          console.log("加载组件:", route.path, "->", route.component);
          route.component = loadView(route.component);
        } catch (error) {
          console.error("加载组件失败:", route.path, route.component, error);
          // 组件加载失败时，设置为空组件或默认组件
          route.component = () => import("@/views/404");
        }
      } else if (typeof route.component === 'string' && route.component.trim() === "") {
        // 组件路径为空时，设置为空组件
        console.log("组件路径为空，设置404:", route.path);
        route.component = () => import("@/views/404");
      } else if (route.component === null) {
        // 组件为null时，设置为空组件
        console.log("组件为null，设置404:", route.path);
        route.component = () => import("@/views/404");
      }
    } else {
      // 没有组件时，设置为空组件
      console.log("没有组件，设置404:", route.path);
      route.component = () => import("@/views/404");
    }

    if (route.children != null && route.children && route.children.length) {
      console.log("处理子路由:", route.path, "子路由数量:", route.children.length);
      route.children = filterAsyncRouter(route.children);
    }

    console.log("路由处理完成:", route.path, "组件类型:", typeof route.component);
    return true;
  });
}

export const loadView = (view) => {
  // 路由懒加载
  console.log("加载组件:", view);

  // 检查组件路径是否有效
  if (!view || typeof view !== 'string' || view.trim() === '') {
    console.error("无效的组件路径:", view);
    return () => import("@/views/404");
  }

  // 处理不同的组件路径格式
  let componentPath = view.trim();

  // 如果路径以 / 开头，去掉开头的 /
  if (componentPath.startsWith('/')) {
    componentPath = componentPath.substring(1);
  }

  // 如果路径不以 .vue 结尾，添加 /index
  if (!componentPath.endsWith('.vue')) {
    componentPath = componentPath + '/index';
  }

  console.log("最终组件路径:", componentPath);

  return (resolve, reject) => {
    require([`@/views/${componentPath}`], resolve, (error) => {
      console.error("组件加载失败:", componentPath, error);
      // 加载失败时返回404页面
      require(['@/views/404'], resolve);
    });
  };
};

const state = {
  routes: [],
  addRoutes: [],
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes;
    state.routes = constantRoutes.concat(routes);
  },
};

const actions = {
  // 生成路由
  generateRoutes({ commit }) {
    return new Promise((resolve) => {
      // 向后端请求路由数据
      getRouters()
        .then((res) => {
          console.log("获取路由数据:", res);
          console.log("路由数据详情:", res.data);

          // 后端返回的数据结构包含routerList和permsList
          const routerList = (res.data && res.data.routerList) || [];

          console.log("处理后的路由列表:", routerList);
          console.log("路由列表长度:", routerList.length);
          console.log("路由列表类型:", Array.isArray(routerList));

          // 确保routerList是数组
          const validRouterList = Array.isArray(routerList) ? routerList : [];

          // 生成路由
          const accessedRoutes = generateRoutes(validRouterList);
          console.log("生成的访问路由:", accessedRoutes);
          console.log("访问路由长度:", accessedRoutes.length);
          console.log("第一个路由详情:", accessedRoutes[0]);

          commit("SET_ROUTES", accessedRoutes);
          resolve(accessedRoutes);
        })
        .catch((error) => {
          console.error("获取路由数据失败:", error);
          console.error("错误详情:", error.response);
          console.error("错误状态:", error.response && error.response.status);
          console.error("错误数据:", error.response && error.response.data);
          // 出错时返回空路由
          commit("SET_ROUTES", []);
          resolve([]);
        });
    });
  },
  // 重置路由
  resetRoutes({ commit }) {
    commit("SET_ROUTES", []);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};

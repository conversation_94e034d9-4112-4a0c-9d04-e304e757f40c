import request from '@/utils/request'

// ==================== 操作日志管理 ====================
// 获取操作日志分页列表
export function getOperLogList(page, limit, searchObj) {
  return request({
    url: `/sysOperLog/${page}/${limit}`,
    method: 'post',
    data: searchObj || {}
  })
}

// 根据id获取操作日志信息
export function getOperLogById(id) {
  return request({
    url: `/sysOperLog/getById/${id}`,
    method: 'get'
  })
}

// 保存系统日志记录
export function saveOperLog(operLog) {
  return request({
    url: '/sysOperLog/saveSysLog',
    method: 'post',
    data: operLog
  })
}

import request from "@/utils/request";

// 获取路由
export function getRouters() {
  return request({
    url: "/sysMenu/getUserMenuList",
    method: "get",
  });
}

// 获取菜单树形结构
export function getMenuList() {
  return request({
    url: "/sysMenu/findNodes",
    method: "get",
  });
}

// 根据id获取菜单信息
export function getMenuById(id) {
  return request({
    url: `/sysMenu/getById/${id}`,
    method: "get",
  });
}

// 保存菜单
export function saveMenu(menu) {
  return request({
    url: "/sysMenu/save",
    method: "post",
    data: menu,
  });
}

// 更新菜单
export function updateMenu(menu) {
  return request({
    url: "/sysMenu/update",
    method: "put",
    data: menu,
  });
}

// 删除菜单
export function removeMenu(id) {
  return request({
    url: `/sysMenu/remove/${id}`,
    method: "delete",
  });
}

// 获取角色菜单权限
export function getMenusByRoleId(roleId) {
  return request({
    url: `/sysMenu/toAssign/${roleId}`,
    method: "get",
  });
}

// 给角色分配菜单权限
export function assignMenus(assignMenuVo) {
  return request({
    url: "/sysMenu/doAssign",
    method: "post",
    data: assignMenuVo,
  });
}

import request from "@/utils/request";

// ==================== 登录相关 ====================
// 登录
export function login(data) {
  return request({
    url: "/securityLogin/login",
    method: "post",
    data,
  });
}

// 获取用户信息
export function getInfo() {
  return request({
    url: "/securityLogin/getUserInfo",
    method: "get",
  });
}

// 退出登录
export function logout() {
  return request({
    url: "/securityLogin/logout",
    method: "post",
  });
}

// 测试连接
export function testConnection() {
  return request({
    url: "/test/hello",
    method: "get",
  });
}

// ==================== 用户管理 ====================
// 获取用户分页列表
export function getUserList(page, limit, searchObj) {
  return request({
    url: `/sysUser/findPage/${page}/${limit}`,
    method: "post",
    data: searchObj || {},
  });
}

// 根据id获取用户信息
export function getUserById(id) {
  return request({
    url: `/sysUser/getById/${id}`,
    method: "get",
  });
}

// 保存用户
export function saveUser(user) {
  return request({
    url: "/sysUser/save",
    method: "post",
    data: user,
  });
}

// 更新用户
export function updateUser(user) {
  return request({
    url: "/sysUser/update",
    method: "put",
    data: user,
  });
}

// 删除用户
export function removeUser(id) {
  return request({
    url: `/sysUser/remove/${id}`,
    method: "delete",
  });
}

// 批量删除用户
export function batchRemoveUser(idList) {
  return request({
    url: "/sysUser/batchRemove",
    method: "delete",
    data: idList,
  });
}

// 更新用户状态
export function updateUserStatus(id, status) {
  return request({
    url: `/sysUser/updateStatus/${id}/${status}`,
    method: "get",
  });
}



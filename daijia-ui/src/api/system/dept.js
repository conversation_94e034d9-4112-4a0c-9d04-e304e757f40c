import request from '@/utils/request'

// ==================== 部门管理 ====================
// 获取部门树形结构
export function getDeptList() {
  return request({
    url: '/sysDept/findNodes',
    method: 'get'
  })
}

// 获取用户部门节点
export function getUserDeptList() {
  return request({
    url: '/sysDept/findUserNodes',
    method: 'get'
  })
}

// 根据id获取部门信息
export function getDeptById(id) {
  return request({
    url: `/sysDept/getById/${id}`,
    method: 'get'
  })
}

// 保存部门
export function saveDept(dept) {
  return request({
    url: '/sysDept/save',
    method: 'post',
    data: dept
  })
}

// 更新部门
export function updateDept(dept) {
  return request({
    url: '/sysDept/update',
    method: 'put',
    data: dept
  })
}

// 删除部门
export function removeDept(id) {
  return request({
    url: `/sysDept/remove/${id}`,
    method: 'delete'
  })
}

// 更新部门状态
export function updateDeptStatus(id, status) {
  return request({
    url: `/sysDept/updateStatus/${id}/${status}`,
    method: 'get'
  })
}

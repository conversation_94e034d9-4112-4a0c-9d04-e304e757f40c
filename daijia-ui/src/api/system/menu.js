import request from '@/utils/request'

// ==================== 菜单管理 ====================
// 获取菜单树形结构
export function getMenuList() {
  return request({
    url: '/sysMenu/findNodes',
    method: 'get'
  })
}

// 根据id获取菜单信息 (后端没有此接口，使用列表数据)
export function getMenuById(id) {
  // 由于后端没有单独获取菜单的接口，这里返回空实现
  // 在实际使用中，会从菜单列表中找到对应的菜单数据
  return Promise.resolve({ data: null })
}

// 保存菜单
export function saveMenu(menu) {
  return request({
    url: '/sysMenu/save',
    method: 'post',
    data: menu
  })
}

// 更新菜单
export function updateMenu(menu) {
  return request({
    url: '/sysMenu/update',
    method: 'put',
    data: menu
  })
}

// 删除菜单
export function removeMenu(id) {
  return request({
    url: `/sysMenu/remove/${id}`,
    method: 'delete'
  })
}

// 获取角色菜单权限
export function getMenusByRoleId(roleId) {
  return request({
    url: `/sysMenu/toAssign/${roleId}`,
    method: 'get'
  })
}

// 给角色分配菜单权限
export function assignMenus(assignMenuVo) {
  return request({
    url: '/sysMenu/doAssign',
    method: 'post',
    data: assignMenuVo
  })
}

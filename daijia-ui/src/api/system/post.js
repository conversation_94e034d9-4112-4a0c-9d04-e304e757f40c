import request from "@/utils/request";

// ==================== 岗位管理 ====================
// 获取岗位分页列表
export function getPostList(page, limit, searchObj) {
  return request({
    url: `/sysPost/${page}/${limit}`,
    method: "post",
    data: searchObj || {},
  });
}

// 获取所有岗位列表
export function getAllPosts() {
  return request({
    url: "/sysPost/findAll",
    method: "get",
  });
}

// 根据id获取岗位信息
export function getPostById(id) {
  return request({
    url: `/sysPost/getById/${id}`,
    method: "get",
  });
}

// 保存岗位
export function savePost(post) {
  return request({
    url: "/sysPost/save",
    method: "post",
    data: post,
  });
}

// 更新岗位
export function updatePost(post) {
  return request({
    url: "/sysPost/update",
    method: "put",
    data: post,
  });
}

// 删除岗位
export function removePost(id) {
  return request({
    url: `/sysPost/remove/${id}`,
    method: "delete",
  });
}

// 更新岗位状态
export function updatePostStatus(id, status) {
  return request({
    url: `/sysPost/updateStatus/${id}/${status}`,
    method: "get",
  });
}

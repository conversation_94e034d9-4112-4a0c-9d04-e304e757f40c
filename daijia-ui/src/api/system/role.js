import request from '@/utils/request'

// ==================== 角色管理 ====================
// 获取角色分页列表
export function getRoleList(page, limit, searchObj) {
  return request({
    url: `/sysRole/findPage/${page}/${limit}`,
    method: 'post',
    data: searchObj || {}
  })
}

// 获取所有角色列表
export function getAllRoles() {
  return request({
    url: '/sysRole/findAll',
    method: 'get'
  })
}

// 根据id获取角色信息
export function getRoleById(id) {
  return request({
    url: `/sysRole/getById/${id}`,
    method: 'get'
  })
}

// 保存角色
export function saveRole(role) {
  return request({
    url: '/sysRole/save',
    method: 'post',
    data: role
  })
}

// 更新角色
export function updateRole(role) {
  return request({
    url: '/sysRole/update',
    method: 'put',
    data: role
  })
}

// 删除角色
export function removeRole(id) {
  return request({
    url: `/sysRole/remove/${id}`,
    method: 'delete'
  })
}

// 批量删除角色
export function batchRemoveRole(idList) {
  return request({
    url: '/sysRole/batchRemove',
    method: 'delete',
    data: idList
  })
}

// 根据用户获取角色数据
export function getRolesByUserId(userId) {
  return request({
    url: `/sysRole/toAssign/${userId}`,
    method: 'get'
  })
}

// 给用户分配角色
export function assignRoles(assignRoleVo) {
  return request({
    url: '/sysRole/doAssign',
    method: 'post',
    data: assignRoleVo
  })
}

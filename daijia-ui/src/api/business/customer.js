import request from '@/utils/request'

// ==================== 客户管理 ====================
// 获取客户分页列表
export function getCustomerList(page, limit, searchObj) {
  return request({
    url: `/customer/info/${page}/${limit}`,
    method: 'post',
    data: searchObj || {}
  })
}

// 根据id获取客户信息
export function getCustomerById(id) {
  return request({
    url: `/customer/info/getById/${id}`,
    method: 'get'
  })
}

// 更新客户状态
export function updateCustomerStatus(id, status) {
  return request({
    url: `/customer/info/updateStatus/${id}/${status}`,
    method: 'get'
  })
}

// 获取客户统计信息
export function getCustomerStats() {
  return request({
    url: '/customer/info/stats',
    method: 'get'
  })
}

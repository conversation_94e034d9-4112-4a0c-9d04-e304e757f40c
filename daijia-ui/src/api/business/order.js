import request from '@/utils/request'

// ==================== 订单管理 ====================
// 获取订单分页列表
export function getOrderList(page, limit, searchObj) {
  return request({
    url: `/order/info/${page}/${limit}`,
    method: 'post',
    data: searchObj || {}
  })
}

// 根据id获取订单信息
export function getOrderById(id) {
  return request({
    url: `/order/info/getById/${id}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status) {
  return request({
    url: `/order/info/updateStatus/${id}/${status}`,
    method: 'get'
  })
}

// 获取订单统计信息
export function getOrderStats() {
  return request({
    url: '/order/info/stats',
    method: 'get'
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: `/order/info/detail/${id}`,
    method: 'get'
  })
}

// 取消订单
export function cancelOrder(id, reason) {
  return request({
    url: `/order/info/cancel/${id}`,
    method: 'post',
    data: { reason }
  })
}

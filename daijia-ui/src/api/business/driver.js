import request from '@/utils/request'

// ==================== 司机管理 ====================
// 获取司机分页列表
export function getDriverList(page, limit, searchObj) {
  return request({
    url: `/driver/info/${page}/${limit}`,
    method: 'post',
    data: searchObj || {}
  })
}

// 根据id获取司机信息
export function getDriverById(id) {
  return request({
    url: `/driver/info/getById/${id}`,
    method: 'get'
  })
}

// 更新司机状态
export function updateDriverStatus(id, status) {
  return request({
    url: `/driver/info/updateStatus/${id}/${status}`,
    method: 'get'
  })
}

// 司机审核
export function auditDriver(id, status) {
  return request({
    url: `/driver/info/audit/${id}/${status}`,
    method: 'get'
  })
}

// 获取司机统计信息
export function getDriverStats() {
  return request({
    url: '/driver/info/stats',
    method: 'get'
  })
}

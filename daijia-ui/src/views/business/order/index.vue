<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户手机" prop="customerPhone">
        <el-input
          v-model="queryParams.customerPhone"
          placeholder="请输入客户手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="司机手机" prop="driverPhone">
        <el-input
          v-model="queryParams.driverPhone"
          placeholder="请输入司机手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="订单状态" clearable size="small">
          <el-option key="1" label="等待接单" value="1" />
          <el-option key="2" label="已接单" value="2" />
          <el-option key="3" label="司机已到达" value="3" />
          <el-option key="4" label="更新代驾车辆信息" value="4" />
          <el-option key="5" label="开始服务" value="5" />
          <el-option key="6" label="结束服务" value="6" />
          <el-option key="7" label="发起收款" value="7" />
          <el-option key="8" label="系统奖励" value="8" />
          <el-option key="9" label="订单完成" value="9" />
          <el-option key="-1" label="订单取消" value="-1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 订单数据表格 -->
    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="订单编号" align="center" prop="id" />
      <el-table-column label="订单号" align="center" prop="orderNo" :show-overflow-tooltip="true" />
      <el-table-column label="客户信息" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.customerName }}</div>
          <div style="color: #909399; font-size: 12px;">{{ scope.row.customerPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="司机信息" align="center" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.driverName">{{ scope.row.driverName }}</div>
          <div v-if="scope.row.driverPhone" style="color: #909399; font-size: 12px;">{{ scope.row.driverPhone }}</div>
          <div v-if="!scope.row.driverName" style="color: #C0C4CC;">未分配</div>
        </template>
      </el-table-column>
      <el-table-column label="起点" align="center" prop="startLocation" :show-overflow-tooltip="true" />
      <el-table-column label="终点" align="center" prop="endLocation" :show-overflow-tooltip="true" />
      <el-table-column label="订单金额" align="center" prop="amount">
        <template slot-scope="scope">
          <span style="color: #E6A23C;">¥{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusTag(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            v-if="scope.row.status > 0 && scope.row.status < 9"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单编号：">{{ form.id }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单号：">{{ form.orderNo }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户姓名：">{{ form.customerName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户手机：">{{ form.customerPhone }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="司机姓名：">{{ form.driverName || '未分配' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="司机手机：">{{ form.driverPhone || '未分配' }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="起点地址：">{{ form.startLocation }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="终点地址：">{{ form.endLocation }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估距离：">{{ form.expectDistance }}公里</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估时间：">{{ form.expectTime }}分钟</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估金额：">¥{{ form.expectAmount }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际金额：">¥{{ form.amount }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单状态：">
              <el-tag :type="getStatusTag(form.status)">
                {{ getStatusText(form.status) }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付方式：">{{ form.payWay === 1 ? '微信支付' : '支付宝' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间：">{{ parseTime(form.createTime) }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新时间：">{{ parseTime(form.updateTime) }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderList, getOrderById, cancelOrder } from "@/api/business/order"

export default {
  name: "Order",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: undefined,
        customerPhone: undefined,
        driverPhone: undefined,
        status: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      getOrderList(this.queryParams.pageNum, this.queryParams.pageSize, this.queryParams).then(response => {
        this.orderList = response.data.records || [];
        this.total = response.data.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.form = row;
      this.open = true;
    },
    /** 取消订单操作 */
    handleCancel(row) {
      this.$confirm('确认要取消订单"' + row.orderNo + '"吗?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          return cancelOrder(row.id, '管理员取消');
        }).then(() => {
          this.$message.success("取消成功");
          this.getList();
        });
    },
    // 订单状态文本
    getStatusText(status) {
      const statusMap = {
        1: '等待接单',
        2: '已接单',
        3: '司机已到达',
        4: '更新代驾车辆信息',
        5: '开始服务',
        6: '结束服务',
        7: '发起收款',
        8: '系统奖励',
        9: '订单完成',
        '-1': '订单取消'
      };
      return statusMap[status] || '未知状态';
    },
    // 订单状态标签颜色
    getStatusTag(status) {
      const tagMap = {
        1: 'warning',
        2: 'primary',
        3: 'primary',
        4: 'primary',
        5: 'success',
        6: 'success',
        7: 'success',
        8: 'success',
        9: 'success',
        '-1': 'danger'
      };
      return tagMap[status] || '';
    }
  }
};
</script>

<template>
  <div class="dashboard-container">
    <div class="app-container">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>欢迎使用代驾后台管理系统</span>
            </div>
            <div class="welcome-content">
              <h2>系统概览</h2>
              <p>这是一个基于Vue2 + Element-UI构建的代驾后台管理系统</p>
              
              <el-row :gutter="20" style="margin-top: 30px;">
                <el-col :span="6">
                  <el-card class="stat-card">
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="el-icon-user" style="color: #409EFF;"></i>
                      </div>
                      <div class="stat-content">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">用户总数</div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="6">
                  <el-card class="stat-card">
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="el-icon-truck" style="color: #67C23A;"></i>
                      </div>
                      <div class="stat-content">
                        <div class="stat-number">567</div>
                        <div class="stat-label">司机总数</div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="6">
                  <el-card class="stat-card">
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="el-icon-s-order" style="color: #E6A23C;"></i>
                      </div>
                      <div class="stat-content">
                        <div class="stat-number">8,901</div>
                        <div class="stat-label">订单总数</div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="6">
                  <el-card class="stat-card">
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="el-icon-money" style="color: #F56C6C;"></i>
                      </div>
                      <div class="stat-content">
                        <div class="stat-number">¥123,456</div>
                        <div class="stat-label">总收入</div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>

              <div style="margin-top: 30px;">
                <h3>功能模块</h3>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-card class="feature-card">
                      <h4><i class="el-icon-setting"></i> 系统管理</h4>
                      <p>用户管理、角色管理、菜单管理、部门管理、岗位管理</p>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card class="feature-card">
                      <h4><i class="el-icon-document"></i> 日志管理</h4>
                      <p>登录日志、操作日志记录与查询</p>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card class="feature-card">
                      <h4><i class="el-icon-s-cooperation"></i> 业务管理</h4>
                      <p>客户管理、司机管理、订单管理</p>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard'
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-content {
  text-align: center;
  
  h2 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    font-size: 16px;
  }
}

.stat-card {
  .stat-item {
    display: flex;
    align-items: center;
    
    .stat-icon {
      font-size: 40px;
      margin-right: 15px;
    }
    
    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
}

.feature-card {
  text-align: center;
  margin-bottom: 20px;
  
  h4 {
    color: #409EFF;
    margin-bottom: 10px;
    
    i {
      margin-right: 8px;
    }
  }
  
  p {
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }
}
</style>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="用户名称" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="用户状态"
          clearable
          size="small"
        >
          <el-option key="1" label="正常" :value="1" />
          <el-option key="0" label="停用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 用户数据表格 -->
    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户编号" align="center" prop="id" />
      <el-table-column
        label="用户名称"
        align="center"
        prop="username"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="用户昵称"
        align="center"
        prop="name"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="手机号码"
        align="center"
        prop="phone"
        width="120"
      />
      <el-table-column label="用户角色" align="center" width="150">
        <template slot-scope="scope">
          <el-tag
            v-for="role in scope.row.roleList"
            :key="role.id"
            size="mini"
            style="margin-right: 5px; margin-bottom: 2px"
          >
            {{ role.roleName }}
          </el-tag>
          <span
            v-if="!scope.row.roleList || scope.row.roleList.length === 0"
            style="color: #c0c4cc"
            >未分配角色</span
          >
        </template>
      </el-table-column>
      <el-table-column label="头像" align="center" prop="headUrl" width="100">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.headUrl"
            style="width: 40px; height: 40px"
            :src="scope.row.headUrl"
            :preview-src-list="[scope.row.headUrl]"
            fit="cover"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="name">
              <el-input v-model="form.name" placeholder="请输入用户昵称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-select
                v-model="form.deptId"
                placeholder="请选择归属部门"
                style="width: 100%"
              >
                <el-option
                  v-for="dept in deptOptions"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位" prop="postId">
              <el-select
                v-model="form.postId"
                placeholder="请选择岗位"
                style="width: 100%"
              >
                <el-option
                  v-for="post in postOptions"
                  :key="post.id"
                  :label="post.name"
                  :value="post.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="头像地址" prop="headUrl">
              <el-input v-model="form.headUrl" placeholder="请输入头像地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号码"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入邮箱"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              v-if="form.id == undefined"
              label="用户名称"
              prop="username"
            >
              <el-input v-model="form.username" placeholder="请输入用户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.id == undefined"
              label="用户密码"
              prop="password"
            >
              <el-input
                v-model="form.password"
                placeholder="请输入用户密码"
                type="password"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="分配角色">
              <el-checkbox-group
                v-model="form.roleIdList"
                v-if="allRoles.length > 0"
              >
                <el-checkbox
                  v-for="role in allRoles"
                  :key="role.id"
                  :label="role.id"
                  style="margin-right: 15px; margin-bottom: 8px"
                  >{{ role.roleName }}</el-checkbox
                >
              </el-checkbox-group>
              <div v-else style="color: #999">加载角色列表中...</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getUserList,
  getUserById,
  saveUser,
  updateUser,
  removeUser,
  updateUserStatus,
} from "@/api/user";
import { getDeptList } from "@/api/system/dept";
import { getAllPosts } from "@/api/system/post";
import { getAllRoles, getRolesByUserId, assignRoles } from "@/api/system/role";

export default {
  name: "User",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 部门选项
      deptOptions: [],
      // 岗位选项
      postOptions: [],
      // 是否显示弹出层
      open: false,
      // 所有角色列表
      allRoles: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: undefined,
        phone: undefined,
        status: undefined,
      },
      // 表单参数
      form: {
        id: undefined,
        deptId: undefined,
        postId: undefined,
        username: undefined,
        name: undefined,
        password: undefined,
        phone: undefined,
        email: undefined,
        headUrl: undefined,
        status: 1,
        description: undefined,
        roleIdList: [],
      },
      // 表单校验
      rules: {
        username: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getDeptOptions();
    this.getPostOptions();
    this.getAllRoleOptions();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      getUserList(
        this.queryParams.pageNum,
        this.queryParams.pageSize,
        this.queryParams
      )
        .then((response) => {
          this.userList = response.data.records || [];
          this.total = response.data.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 获取部门选项 */
    getDeptOptions() {
      getDeptList().then((response) => {
        this.deptOptions = response.data || [];
      });
    },
    /** 获取岗位选项 */
    getPostOptions() {
      getAllPosts().then((response) => {
        this.postOptions = response.data || [];
      });
    },
    /** 获取所有角色选项 */
    getAllRoleOptions() {
      getAllRoles().then((response) => {
        this.allRoles = response.data || [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deptId: undefined,
        postId: undefined,
        username: undefined,
        name: undefined,
        password: undefined,
        phone: undefined,
        email: undefined,
        headUrl: undefined,
        status: 1,
        description: undefined,
        roleIdList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.id || this.ids;

      console.log("开始修改用户，用户ID:", userId);

      // 确保角色列表已加载
      if (this.allRoles.length === 0) {
        this.getAllRoleOptions();
      }

      // 获取用户基本信息
      getUserById(userId)
        .then((response) => {
          console.log("获取用户信息成功:", response.data);
          // 使用Object.assign确保保留form的初始结构
          this.form = Object.assign({}, this.form, response.data);

          // 获取用户已分配的角色
          getRolesByUserId(userId)
            .then((roleResponse) => {
              console.log("获取用户角色信息:", roleResponse.data);

              // 处理后端返回的数据结构
              let assignRoles = [];
              if (roleResponse.data && roleResponse.data.assginRoleList) {
                // 后端返回的字段名是 assginRoleList
                assignRoles = roleResponse.data.assginRoleList;
              } else if (roleResponse.data && roleResponse.data.assignRoles) {
                // 兼容其他可能的字段名
                assignRoles = roleResponse.data.assignRoles;
              } else if (
                roleResponse.data &&
                Array.isArray(roleResponse.data)
              ) {
                assignRoles = roleResponse.data;
              }

              console.log("用户已分配角色:", assignRoles);

              // 使用Vue.set确保响应式更新
              this.$set(
                this.form,
                "roleIdList",
                assignRoles.map((role) => role.id)
              );
              console.log("设置表单角色ID列表:", this.form.roleIdList);

              // 延迟打开对话框，确保数据已设置
              this.$nextTick(() => {
                this.open = true;
                this.title = "修改用户";
              });
            })
            .catch((error) => {
              console.error("获取用户角色失败:", error);
              // 即使获取角色失败，也要打开对话框
              this.$set(this.form, "roleIdList", []);
              this.open = true;
              this.title = "修改用户";
            });
        })
        .catch((error) => {
          console.error("获取用户信息失败:", error);
          this.$message.error("获取用户信息失败");
        });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            // 修改用户
            updateUser(this.form).then((response) => {
              // 如果有角色信息，同时更新角色分配
              if (this.form.roleIdList && this.form.roleIdList.length > 0) {
                const assignRoleVo = {
                  userId: this.form.id,
                  roleIdList: this.form.roleIdList,
                };
                assignRoles(assignRoleVo).then(() => {
                  this.$message.success("修改成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                this.$message.success("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            // 新增用户
            saveUser(this.form).then((response) => {
              const newUserId = response.data.id || response.data;

              // 如果有角色信息，分配角色
              if (
                this.form.roleIdList &&
                this.form.roleIdList.length > 0 &&
                newUserId
              ) {
                const assignRoleVo = {
                  userId: newUserId,
                  roleIdList: this.form.roleIdList,
                };
                assignRoles(assignRoleVo).then(() => {
                  this.$message.success("新增成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                this.$message.success("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.id || this.ids;
      this.$confirm(
        '是否确认删除用户编号为"' + userIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return removeUser(userIds);
        })
        .then(() => {
          this.getList();
          this.$message.success("删除成功");
        });
    },
    /** 用户状态修改  */
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.username + '"用户吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return updateUserStatus(row.id, row.status);
        })
        .then(() => {
          this.$message.success(text + "成功");
        })
        .catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
  },
};
</script>

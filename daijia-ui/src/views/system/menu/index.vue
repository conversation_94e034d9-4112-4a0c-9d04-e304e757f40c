<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>菜单管理</span>
        <div style="float: right">
          <el-button
            type="text"
            icon="el-icon-s-unfold"
            @click="expandAll"
            style="margin-right: 10px"
            >展开全部</el-button
          >
          <el-button
            type="text"
            icon="el-icon-s-fold"
            @click="collapseAll"
            style="margin-right: 10px"
            >折叠全部</el-button
          >
          <el-button
            v-hasPermission="['bnt.sysMenu.add']"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd"
            >新增</el-button
          >
        </div>
      </div>
      <el-table
        ref="menuTable"
        :data="menuList"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column
          prop="name"
          label="菜单名称"
          width="180"
        ></el-table-column>
        <el-table-column prop="icon" label="图标" align="center" width="80">
          <template slot-scope="scope">
            <i :class="scope.row.icon"></i>
          </template>
        </el-table-column>
        <el-table-column
          prop="perms"
          label="权限标识"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="path"
          label="路由地址"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="component"
          label="组件路径"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="sortValue"
          label="排序"
          width="60"
        ></el-table-column>
        <el-table-column label="类型" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 0" type="success">目录</el-tag>
            <el-tag v-if="scope.row.type === 1" type="primary">菜单</el-tag>
            <el-tag v-if="scope.row.type === 2" type="warning">按钮</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
            <el-tag v-else type="info">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template slot-scope="scope">
            <el-button
              v-hasPermission="['bnt.sysMenu.update']"
              type="text"
              size="mini"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              v-hasPermission="['bnt.sysMenu.add']"
              v-if="scope.row.type !== 2"
              type="text"
              size="mini"
              @click="handleAddChild(scope.row)"
              >添加下级</el-button
            >
            <el-button
              v-hasPermission="['bnt.sysMenu.remove']"
              type="text"
              size="mini"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级菜单">
              <treeselect
                v-model="form.parentId"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio :label="0">目录</el-radio>
                <el-radio :label="1">菜单</el-radio>
                <el-radio :label="2">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.type !== 2" label="菜单图标" prop="icon">
              <el-input v-model="form.icon" placeholder="菜单图标">
                <el-button slot="append" @click="showIconDialog = true"
                  >选择图标</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="sortValue">
              <el-input-number
                v-model="form.sortValue"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type !== 2">
            <el-form-item label="路由地址" prop="path">
              <el-input v-model="form.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type === 1">
            <el-form-item label="组件路径" prop="component">
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type === 2">
            <el-form-item label="权限标识" prop="perms">
              <el-input
                v-model="form.perms"
                placeholder="请输入权限标识"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type !== 2">
            <el-form-item label="菜单状态">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type !== 2">
            <el-form-item label="是否隐藏">
              <el-radio-group v-model="form.isHide">
                <el-radio :label="0">显示</el-radio>
                <el-radio :label="1">隐藏</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type === 1">
            <el-form-item label="高亮路径">
              <el-input
                v-model="form.activeMenu"
                placeholder="请输入高亮路径"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图标选择对话框 -->
    <el-dialog
      title="选择图标"
      :visible.sync="showIconDialog"
      width="800px"
      append-to-body
    >
      <div class="icon-list">
        <div
          v-for="icon in iconList"
          :key="icon"
          class="icon-item"
          @click="selectIcon(icon)"
        >
          <i :class="icon"></i>
          <span>{{ icon }}</span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showIconDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMenuList,
  getMenuById,
  saveMenu,
  updateMenu,
  removeMenu,
} from "@/api/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Menu",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单表格数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示图标选择对话框
      showIconDialog: false,
      // 是否展开全部
      isExpandAll: true,
      // 表单参数
      form: {
        id: undefined,
        parentId: 0,
        name: undefined,
        type: 0,
        path: undefined,
        component: undefined,
        perms: undefined,
        icon: undefined,
        sortValue: 1,
        status: 1,
        isHide: 0,
        activeMenu: undefined,
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        path: [
          { required: true, message: "路由地址不能为空", trigger: "blur" },
        ],
        component: [
          { required: true, message: "组件路径不能为空", trigger: "blur" },
        ],
      },
      // 图标列表
      iconList: [
        "el-icon-platform-eleme",
        "el-icon-eleme",
        "el-icon-delete-solid",
        "el-icon-delete",
        "el-icon-s-tools",
        "el-icon-setting",
        "el-icon-user-solid",
        "el-icon-user",
        "el-icon-phone",
        "el-icon-phone-outline",
        "el-icon-more",
        "el-icon-more-outline",
        "el-icon-star-on",
        "el-icon-star-off",
        "el-icon-s-goods",
        "el-icon-goods",
        "el-icon-warning",
        "el-icon-warning-outline",
        "el-icon-question",
        "el-icon-info",
        "el-icon-remove",
        "el-icon-circle-plus",
        "el-icon-success",
        "el-icon-error",
        "el-icon-zoom-in",
        "el-icon-zoom-out",
        "el-icon-remove-outline",
        "el-icon-circle-plus-outline",
        "el-icon-circle-check",
        "el-icon-circle-close",
        "el-icon-s-help",
        "el-icon-help",
        "el-icon-minus",
        "el-icon-plus",
        "el-icon-check",
        "el-icon-close",
        "el-icon-picture",
        "el-icon-picture-outline",
        "el-icon-picture-outline-round",
        "el-icon-upload",
        "el-icon-upload2",
        "el-icon-download",
        "el-icon-camera-solid",
        "el-icon-camera",
        "el-icon-video-camera-solid",
        "el-icon-video-camera",
        "el-icon-message-solid",
        "el-icon-bell",
        "el-icon-s-cooperation",
        "el-icon-s-order",
        "el-icon-s-platform",
        "el-icon-s-fold",
        "el-icon-s-unfold",
        "el-icon-s-operation",
        "el-icon-s-promotion",
        "el-icon-s-home",
        "el-icon-s-release",
        "el-icon-s-ticket",
        "el-icon-s-management",
        "el-icon-s-open",
        "el-icon-s-shop",
        "el-icon-s-marketing",
        "el-icon-s-flag",
        "el-icon-s-comment",
        "el-icon-s-finance",
        "el-icon-s-claim",
        "el-icon-s-custom",
        "el-icon-s-opportunity",
        "el-icon-s-data",
        "el-icon-s-check",
        "el-icon-s-grid",
        "el-icon-menu",
        "el-icon-share",
        "el-icon-d-caret",
        "el-icon-caret-left",
        "el-icon-caret-right",
        "el-icon-caret-bottom",
        "el-icon-caret-top",
        "el-icon-bottom-left",
        "el-icon-bottom-right",
        "el-icon-back",
        "el-icon-right",
        "el-icon-bottom",
        "el-icon-top",
        "el-icon-top-left",
        "el-icon-top-right",
        "el-icon-arrow-left",
        "el-icon-arrow-right",
        "el-icon-arrow-down",
        "el-icon-arrow-up",
        "el-icon-d-arrow-left",
        "el-icon-d-arrow-right",
        "el-icon-video-pause",
        "el-icon-video-play",
        "el-icon-refresh",
        "el-icon-refresh-right",
        "el-icon-refresh-left",
        "el-icon-finished",
        "el-icon-sort",
        "el-icon-sort-up",
        "el-icon-sort-down",
        "el-icon-rank",
        "el-icon-loading",
        "el-icon-view",
        "el-icon-c-scale-to-original",
        "el-icon-date",
        "el-icon-edit",
        "el-icon-edit-outline",
        "el-icon-folder",
        "el-icon-folder-opened",
        "el-icon-folder-add",
        "el-icon-folder-remove",
        "el-icon-folder-delete",
        "el-icon-folder-checked",
        "el-icon-tickets",
        "el-icon-document-remove",
        "el-icon-document-delete",
        "el-icon-document-copy",
        "el-icon-document-checked",
        "el-icon-document",
        "el-icon-document-add",
        "el-icon-printer",
        "el-icon-paperclip",
        "el-icon-takeaway-box",
        "el-icon-search",
        "el-icon-monitor",
        "el-icon-attract",
        "el-icon-mobile",
        "el-icon-scissors",
        "el-icon-umbrella",
        "el-icon-headset",
        "el-icon-brush",
        "el-icon-mouse",
        "el-icon-coordinate",
        "el-icon-magic-stick",
        "el-icon-reading",
        "el-icon-data-line",
        "el-icon-data-board",
        "el-icon-pie-chart",
        "el-icon-data-analysis",
        "el-icon-collection-tag",
        "el-icon-film",
        "el-icon-suitcase",
        "el-icon-suitcase-1",
        "el-icon-receiving",
        "el-icon-collection",
        "el-icon-files",
        "el-icon-notebook-1",
        "el-icon-notebook-2",
        "el-icon-toilet-paper",
        "el-icon-office-building",
        "el-icon-school",
        "el-icon-table-lamp",
        "el-icon-house",
        "el-icon-no-smoking",
        "el-icon-smoking",
        "el-icon-shopping-cart-full",
        "el-icon-shopping-cart-1",
        "el-icon-shopping-cart-2",
        "el-icon-shopping-bag-1",
        "el-icon-shopping-bag-2",
        "el-icon-sold-out",
        "el-icon-sell",
        "el-icon-present",
        "el-icon-box",
        "el-icon-bank-card",
        "el-icon-money",
        "el-icon-coin",
        "el-icon-wallet",
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      getMenuList().then((response) => {
        this.menuList = response.data;
        this.loading = false;
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getMenuList().then((response) => {
        this.menuOptions = [];
        const menu = { id: 0, name: "主目录", children: [] };
        menu.children = response.data;
        this.menuOptions.push(menu);
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        parentId: 0,
        name: undefined,
        type: 0,
        path: undefined,
        component: undefined,
        perms: undefined,
        icon: undefined,
        sortValue: 1,
        status: 1,
        isHide: 0,
        activeMenu: undefined,
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect();
      this.open = true;
      this.title = "添加菜单";
    },
    /** 添加子菜单 */
    handleAddChild(row) {
      this.reset();
      this.getTreeselect();
      this.form.parentId = row.id;
      this.open = true;
      this.title = "添加子菜单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getMenuById(row.id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改菜单";
      });
    },
    refreshUserMenu() {
      // 重新拉取用户信息和权限路由
      this.$store.dispatch("user/getInfo").then(() => {
        this.$store
          .dispatch("permission/generateRoutes")
          .then((accessRoutes) => {
            this.$router.addRoutes(accessRoutes);
          });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateMenu(this.form).then((response) => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
              this.refreshUserMenu();
            });
          } else {
            saveMenu(this.form).then((response) => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
              this.refreshUserMenu();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // 检查是否有子菜单
      if (row.children && row.children.length > 0) {
        this.$message.error("该菜单下有子菜单，请先删除子菜单");
        return;
      }

      this.$confirm('是否确认删除名称为"' + row.name + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeMenu(row.id);
        })
        .then(() => {
          this.getList();
          this.$message.success("删除成功");
          this.refreshUserMenu();
        })
        .catch((error) => {
          // 如果是后端返回的错误信息
          if (error && error.response && error.response.data) {
            const { message } = error.response.data;
            if (message && message.includes("子节点")) {
              this.$message.error("该菜单下有子菜单，请先删除子菜单");
            } else {
              this.$message.error(message || "删除失败");
            }
          }
        });
    },
    /** 选择图标 */
    selectIcon(icon) {
      this.form.icon = icon;
      this.showIconDialog = false;
    },
    /** 展开全部 */
    expandAll() {
      this.isExpandAll = true;
      this.$nextTick(() => {
        this.toggleRowExpansion(this.menuList, true);
      });
    },
    /** 折叠全部 */
    collapseAll() {
      this.isExpandAll = false;
      this.$nextTick(() => {
        this.toggleRowExpansion(this.menuList, false);
      });
    },
    /** 递归展开/折叠行 */
    toggleRowExpansion(data, isExpand) {
      data.forEach((item) => {
        this.$refs.menuTable.toggleRowExpansion(item, isExpand);
        if (item.children && item.children.length > 0) {
          this.toggleRowExpansion(item.children, isExpand);
        }
      });
    },
  },
};
</script>

<style scoped>
.icon-list {
  display: flex;
  flex-wrap: wrap;
  max-height: 400px;
  overflow-y: auto;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 5px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-item i {
  font-size: 20px;
  margin-bottom: 5px;
}

.icon-item span {
  font-size: 10px;
  color: #606266;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}
</style>

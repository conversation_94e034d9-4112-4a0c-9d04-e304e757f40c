<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入描述"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="角色编码" prop="roleCode">
        <el-input
          v-model="queryParams.roleCode"
          placeholder="请输入角色编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 角色数据表格 -->
    <el-table
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="角色编号" align="center" prop="id" />
      <el-table-column
        label="角色名称"
        align="center"
        prop="roleName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="角色编码"
        align="center"
        prop="roleCode"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="描述"
        align="center"
        prop="description"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleMenuPermission(scope.row)"
            >分配权限</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="form.roleCode" placeholder="请输入角色编码" />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分配菜单权限对话框 -->
    <el-dialog
      title="分配菜单权限"
      :visible.sync="openMenuPermission"
      width="400px"
      append-to-body
    >
      <el-tree
        :data="menuOptions"
        show-checkbox
        ref="menu"
        node-key="id"
        :check-strictly="false"
        empty-text="加载中，请稍候"
        :props="defaultProps"
        default-expand-all
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMenuPermission"
          >确 定</el-button
        >
        <el-button @click="cancelMenuPermission">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRoleList,
  getRoleById,
  saveRole,
  updateRole,
  removeRole,
} from "@/api/system/role";
import { getMenuList, getMenusByRoleId, assignMenus } from "@/api/system/menu";

export default {
  name: "Role",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示菜单权限弹出层
      openMenuPermission: false,
      // 菜单列表
      menuOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleCode: undefined,
        description: undefined,
      },
      // 表单参数
      form: {},
      // 当前角色ID
      currentRoleId: undefined,
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
        ],
        roleCode: [
          { required: true, message: "角色编码不能为空", trigger: "blur" },
        ],
      },
      // 树形结构属性
      defaultProps: {
        children: "children",
        label: "name",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      getRoleList(
        this.queryParams.pageNum,
        this.queryParams.pageSize,
        this.queryParams
      )
        .then((response) => {
          this.roleList = response.data.records || [];
          this.total = response.data.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        roleName: undefined,
        roleCode: undefined,
        description: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.id || this.ids;
      getRoleById(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改角色";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateRole(this.form).then((response) => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveRole(this.form).then((response) => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.id || this.ids;
      this.$confirm(
        '是否确认删除角色编号为"' + roleIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return removeRole(roleIds);
        })
        .then(() => {
          this.getList();
          this.$message.success("删除成功");
        });
    },

    /** 分配菜单权限操作 */
    handleMenuPermission(row) {
      this.reset();
      this.currentRoleId = row.id;
      this.openMenuPermission = true;
      this.getMenuTreeselect();
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      // 先获取菜单树
      getMenuList().then((response) => {
        this.menuOptions = response.data;
        // 获取菜单树后，再获取角色已有菜单
        getMenusByRoleId(this.currentRoleId).then((response) => {
          // 从返回的数据中提取已选中的菜单ID
          const checkedKeys = this.getCheckedMenuIds(response.data);
          // 设置选中的菜单
          this.$nextTick(() => {
            this.$refs.menu.setCheckedKeys(checkedKeys);
          });
        });
      });
    },

    /** 获取选中的菜单ID */
    getCheckedMenuIds(menus) {
      const checkedKeys = [];
      const findCheckedMenus = (menuList) => {
        menuList.forEach((menu) => {
          if (menu.select) {
            checkedKeys.push(menu.id);
          }
          if (menu.children && menu.children.length > 0) {
            findCheckedMenus(menu.children);
          }
        });
      };
      findCheckedMenus(menus);
      return checkedKeys;
    },
    /** 提交菜单权限 */
    submitMenuPermission() {
      const roleId = this.currentRoleId;
      // 获取选中的节点和半选中的节点
      const checkedKeys = this.$refs.menu.getCheckedKeys();
      const halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      // 合并选中的节点和半选中的节点
      const menuIds = [...checkedKeys, ...halfCheckedKeys];
      const data = {
        roleId: roleId,
        menuIdList: menuIds,
      };
      assignMenus(data).then((response) => {
        this.$message.success("分配成功");
        this.openMenuPermission = false;
      });
    },
    /** 取消菜单权限 */
    cancelMenuPermission() {
      this.openMenuPermission = false;
    },
  },
};
</script>

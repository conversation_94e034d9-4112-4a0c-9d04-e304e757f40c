import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

// 引入样式
import "normalize.css/normalize.css";
import "@/styles/index.scss";

// 引入权限控制
import "@/permission";

// 引入权限指令
import permission from "./directive/permission";
Vue.use(permission);

// 全局组件
import Pagination from "@/components/Pagination";
import RightToolbar from "@/components/RightToolbar";

Vue.config.productionTip = false;

// 使用Element UI
Vue.use(ElementUI, {
  size: "medium", // 设置组件默认尺寸
});

// 全局组件挂载
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);

// 全局方法挂载
Vue.prototype.parseTime = function (time, pattern) {
  if (!time) return "";
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
};

Vue.prototype.resetForm = function (refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
};

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");

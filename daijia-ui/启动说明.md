# 代驾后台管理系统启动说明

## 🚀 快速启动

### 1. 安装依赖
```bash
cd daijia-ui
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问系统
- 前端地址：http://localhost:8080
- 默认账号：admin
- 默认密码：123456

## 📋 功能清单

### ✅ 已完成功能

#### 系统管理
- **用户管理** - 完整的用户CRUD操作，状态管理，角色分配
- **角色管理** - 角色CRUD操作，权限分配功能
- **菜单管理** - 树形菜单管理，图标选择
- **部门管理** - 树形部门结构管理
- **岗位管理** - 岗位信息管理，状态控制

#### 日志管理
- **登录日志** - 用户登录记录查询，支持条件筛选
- **操作日志** - 系统操作记录查询，详情查看

#### 业务管理
- **客户管理** - 客户信息查看，状态管理
- **司机管理** - 司机信息管理，审核功能
- **订单管理** - 订单查询，状态管理，取消功能

### 🎯 核心特性

1. **完整的权限体系**
   - 基于RBAC的权限控制
   - 路由权限守卫
   - 按钮级权限控制

2. **响应式设计**
   - 适配不同屏幕尺寸
   - Element-UI组件库
   - 美观的界面设计

3. **数据交互**
   - 统一的API接口调用
   - 错误处理机制
   - 加载状态提示

4. **用户体验**
   - 搜索和筛选功能
   - 分页展示
   - 操作确认提示

## 🔧 开发说明

### 技术栈
- Vue 2.7.16
- Element-UI 2.15.14
- Vue Router 3.6.5
- Vuex 3.6.2
- Axios 1.6.2

### 项目结构
```
src/
├── api/           # API接口定义
├── components/    # 公共组件
├── layout/        # 布局组件
├── router/        # 路由配置
├── store/         # 状态管理
├── styles/        # 样式文件
├── utils/         # 工具函数
├── views/         # 页面组件
└── main.js        # 入口文件
```

### 开发规范
- 组件命名：PascalCase
- 文件命名：kebab-case
- 变量命名：camelCase
- 常量命名：UPPER_CASE

## 🔗 后端对接

### API代理配置
```javascript
// vue.config.js
proxy: {
  '/api': {
    target: 'http://localhost:8603',
    changeOrigin: true,
    pathRewrite: { '^/api': '' }
  }
}
```

### 接口规范
- 请求格式：JSON
- 响应格式：统一Result封装
- 认证方式：Token认证
- 错误处理：统一错误码

## 📝 使用说明

### 登录系统
1. 打开浏览器访问 http://localhost:8080
2. 输入用户名：admin，密码：123456
3. 点击登录进入系统

### 用户管理
1. 点击"系统管理" -> "用户管理"
2. 可以查看、新增、修改、删除用户
3. 支持按用户名、手机号、状态搜索
4. 可以启用/停用用户状态

### 角色管理
1. 点击"系统管理" -> "角色管理"
2. 可以管理系统角色
3. 点击"分配权限"可以为角色分配菜单权限

### 菜单管理
1. 点击"系统管理" -> "菜单管理"
2. 树形结构展示系统菜单
3. 可以新增、修改、删除菜单
4. 支持图标选择

### 部门管理
1. 点击"系统管理" -> "部门管理"
2. 树形结构管理部门层级
3. 支持部门的增删改查

### 岗位管理
1. 点击"系统管理" -> "岗位管理"
2. 管理系统岗位信息
3. 支持岗位状态控制

### 日志查看
1. 点击"日志管理"查看系统日志
2. 登录日志：查看用户登录记录
3. 操作日志：查看系统操作记录

### 业务管理
1. 客户管理：查看和管理客户信息
2. 司机管理：管理司机信息和审核
3. 订单管理：查看和管理订单信息

## 🐛 常见问题

### 1. 登录失败
- 检查后端服务是否启动
- 确认用户名密码是否正确
- 查看浏览器控制台错误信息

### 2. 页面空白
- 检查路由配置是否正确
- 确认组件是否正确导入
- 查看浏览器控制台错误

### 3. 接口调用失败
- 检查代理配置是否正确
- 确认后端服务地址和端口
- 查看网络请求状态

## 📞 技术支持

如有问题，请检查：
1. Node.js版本是否符合要求
2. 依赖是否正确安装
3. 后端服务是否正常运行
4. 浏览器控制台是否有错误信息

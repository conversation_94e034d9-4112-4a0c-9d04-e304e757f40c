# 代驾后台管理系统前端

基于Vue2 + Element-UI构建的代驾后台管理系统前端项目。

## 技术栈

- **Vue 2.7.16** - 渐进式JavaScript框架
- **Element-UI 2.15.14** - 基于Vue的桌面端组件库
- **Vue Router 3.6.5** - Vue官方路由管理器
- **Vuex 3.6.2** - Vue状态管理模式
- **Axios 1.6.2** - HTTP客户端
- **SCSS** - CSS预处理器

## 环境要求

- Node.js 16+
- npm 或 yarn

## 安装依赖

```bash
npm install
# 或
yarn install
```

## 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

## 构建生产版本

```bash
npm run build
# 或
yarn build
```

## 访问地址

- 开发环境: http://localhost:8080
- 默认账号: admin
- 默认密码: 123456

## 代理配置

前端通过 vue.config.js 代理将 `/api` 请求转发到后端服务 `http://localhost:8603`

## 项目结构

```
src/
├── api/           # API接口
├── assets/        # 静态资源
├── components/    # 公共组件
├── layout/        # 布局组件
├── router/        # 路由配置
├── store/         # 状态管理
├── styles/        # 样式文件
├── utils/         # 工具函数
├── views/         # 页面组件
├── App.vue        # 根组件
├── main.js        # 入口文件
└── permission.js  # 权限控制
```

## 功能模块

### 系统管理
- 用户管理
- 角色管理
- 菜单管理
- 部门管理
- 岗位管理

### 日志管理
- 登录日志
- 操作日志

### 业务管理
- 客户管理
- 司机管理
- 订单管理

## 开发规范

### 命名规范
- 文件夹：kebab-case（短横线命名）
- 组件文件：PascalCase（大驼峰命名）
- 变量/方法：camelCase（小驼峰命名）

### 代码规范
- 使用ESLint进行代码检查
- 统一使用2个空格缩进
- 字符串使用单引号

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

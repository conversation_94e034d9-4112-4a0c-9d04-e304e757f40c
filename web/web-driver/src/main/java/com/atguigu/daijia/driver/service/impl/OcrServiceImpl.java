package com.atguigu.daijia.driver.service.impl;

import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.driver.client.OcrFeignClient;
import com.atguigu.daijia.driver.service.OcrService;
import com.atguigu.daijia.model.vo.driver.DriverLicenseOcrVo;
import com.atguigu.daijia.model.vo.driver.IdCardOcrVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class OcrServiceImpl implements OcrService {


    @Autowired
    private OcrFeignClient ocrFeignClient;

    // 身份验证
    @Override
    public IdCardOcrVo idCardOcr(MultipartFile file, String side) {
        Result<IdCardOcrVo> result = ocrFeignClient.idCardOcr(file,side);
        if (result.getCode() != 200) {
            throw new GuiguException(result.getCode(), result.getMessage());
        }
        return result.getData();
    }

    // 驾驶证验证
    @Override
    public DriverLicenseOcrVo driverLicenseOcr(MultipartFile file) {
        Result<DriverLicenseOcrVo> result = ocrFeignClient.driverLicenseOcr(file);
        if (result.getCode() != 200) {
            throw new GuiguException(result.getCode(), result.getMessage());
        }
        return result.getData();
    }
}

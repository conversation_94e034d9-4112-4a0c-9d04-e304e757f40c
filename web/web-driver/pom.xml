<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.atguigu.daijia</groupId>
		<artifactId>web</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>web-driver</artifactId>
	<version>1.0</version>

	<packaging>jar</packaging>
	<name>web-driver</name>
	<description>web-driver</description>

	<dependencies>
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java-ocr</artifactId>
			<version>3.1.829</version>
		</dependency>
		<dependency>
			<groupId>com.qcloud</groupId>
			<artifactId>cos_api</artifactId>
			<version>5.6.155</version>
		</dependency>
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
		</dependency>
	</dependencies>

	<build>
		<finalName>web-driver</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.atguigu.daijia</groupId>
		<artifactId>daijia-parent</artifactId>
		<version>1.0</version>
	</parent>

	<modules>
		<module>web-customer</module>
		<module>web-driver</module>
		<module>web-mgr</module>
	</modules>

	<artifactId>web</artifactId>
	<packaging>pom</packaging>
	<version>1.0</version>

	<dependencies>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-util</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>common-log</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-system-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-coupon-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-driver-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-order-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-customer-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-map-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-rules-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-dispatch-client</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>service-payment-client</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- 服务调用feign -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>

		<!-- 服务注册 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>

		<!-- 服务配置 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>

		<!-- 流量控制 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

</project>

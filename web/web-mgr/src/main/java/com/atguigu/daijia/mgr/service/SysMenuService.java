package com.atguigu.daijia.mgr.service;


import com.atguigu.daijia.model.entity.system.SysMenu;
import com.atguigu.daijia.model.vo.system.AssginMenuVo;

import java.util.List;
import java.util.Map;

public interface SysMenuService {

    /**
     * 获取用户菜单列表
     * @return
     */
    Map<String, Object> getUserMenuList();

    void save(SysMenu sysMenu);

    void update(SysMenu sysMenu);

    void remove(Long id);

    /**
     * 菜单树形数据
     * @return
     */
    List<SysMenu> findNodes();

    /**
     * 根据ID获取菜单
     * @param id
     * @return
     */
    SysMenu getById(Long id);


    /**
     * 保存角色权限
     * @param  assginMenuVo
     */
    void doAssign(AssginMenuVo assginMenuVo);


    List<SysMenu> toAssign(Long roleId);
}

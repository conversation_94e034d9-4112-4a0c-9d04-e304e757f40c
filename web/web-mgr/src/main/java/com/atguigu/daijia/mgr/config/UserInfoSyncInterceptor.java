package com.atguigu.daijia.mgr.config;

import com.atguigu.daijia.common.util.AuthContextHolder;
import com.atguigu.daijia.common.util.LoginUserInfoHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户信息同步拦截器
 * 将AuthContextHolder中的用户信息同步到LoginUserInfoHelper中
 */
public class UserInfoSyncInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        System.out.println("=== UserInfoSyncInterceptor 执行 ===");
        System.out.println("请求URL: " + request.getRequestURI());
        
        try {
            // 从AuthContextHolder获取用户信息
            Long userId = AuthContextHolder.getUserId();
            System.out.println("从AuthContextHolder获取userId: " + userId);
            
            if (userId != null) {
                // 同步到LoginUserInfoHelper
                LoginUserInfoHelper.setUserId(userId);
                System.out.println("已同步userId到LoginUserInfoHelper: " + userId);
                
                // 设置默认用户名（如果需要的话）
                LoginUserInfoHelper.setUsername("user_" + userId);
                System.out.println("已设置username到LoginUserInfoHelper: user_" + userId);
            } else {
                System.out.println("AuthContextHolder中userId为空");
            }
        } catch (Exception e) {
            System.out.println("用户信息同步失败: " + e.getMessage());
        }
        
        System.out.println("=== UserInfoSyncInterceptor 执行完成 ===");
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求结束后，清除ThreadLocal中的数据
        LoginUserInfoHelper.removeUserId();
        LoginUserInfoHelper.removeUsername();
    }
}

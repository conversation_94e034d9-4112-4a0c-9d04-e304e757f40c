package com.atguigu.daijia.mgr.config;

import com.atguigu.daijia.common.util.LoginUserInfoHelper;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * Feign配置类
 * 用于传递请求头到下游服务
 */
@Configuration
public class FeignConfig {

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate requestTemplate) {
                System.out.println("=== Feign拦截器执行 ===");
                System.out.println("请求URL: " + requestTemplate.url());

                // 方法1：从LoginUserInfoHelper获取用户ID
                try {
                    Long userId = LoginUserInfoHelper.getUserId();
                    String username = LoginUserInfoHelper.getUsername();

                    if (userId != null) {
                        requestTemplate.header("userId", userId.toString());
                        System.out.println("从LoginUserInfoHelper获取userId: " + userId);
                    }

                    if (username != null) {
                        requestTemplate.header("username", username);
                        System.out.println("从LoginUserInfoHelper获取username: " + username);
                    }
                } catch (Exception e) {
                    System.out.println("从LoginUserInfoHelper获取用户信息失败: " + e.getMessage());
                }

                // 方法2：从HTTP请求上下文获取请求头
                try {
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    if (attributes != null) {
                        HttpServletRequest request = attributes.getRequest();
                        System.out.println("从HTTP请求获取请求头");

                        // 传递关键请求头
                        String[] headerNames = {"userId", "username", "token", "username-base64", "username-encoding"};
                        for (String headerName : headerNames) {
                            String headerValue = request.getHeader(headerName);
                            if (headerValue != null) {
                                requestTemplate.header(headerName, headerValue);
                                System.out.println("传递请求头: " + headerName + " = " + headerValue);
                            }
                        }
                    } else {
                        System.out.println("无法获取HTTP请求上下文");
                    }
                } catch (Exception e) {
                    System.out.println("从HTTP请求获取请求头失败: " + e.getMessage());
                }

                System.out.println("=== Feign拦截器执行完成 ===");
            }
        };
    }
}

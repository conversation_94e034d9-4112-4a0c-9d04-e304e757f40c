<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.atguigu.daijia</groupId>
		<artifactId>web</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>web-mgr</artifactId>
	<version>1.0</version>

	<packaging>jar</packaging>
	<name>web-mgr</name>
	<description>web-mgr</description>

	<dependencies>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>spring-security</artifactId>
			<version>1.0</version>
		</dependency>
	</dependencies>


	<build>
		<finalName>web-mgr</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>

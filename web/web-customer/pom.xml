<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.atguigu.daijia</groupId>
		<artifactId>web</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>web-customer</artifactId>
	<version>1.0</version>

	<packaging>jar</packaging>
	<name>web-customer</name>
	<description>web-customer</description>

	<dependencies>
		<dependency>
			<groupId>com.atguigu.daijia</groupId>
			<artifactId>rabbit-util</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-seata</artifactId>
		</dependency>
		<dependency>
			<groupId>io.seata</groupId>
			<artifactId>seata-spring-boot-starter</artifactId>
			<version>1.7.1</version>
		</dependency>
	</dependencies>


	<build>
		<finalName>web-customer</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>

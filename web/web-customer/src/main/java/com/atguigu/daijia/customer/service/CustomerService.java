package com.atguigu.daijia.customer.service;

import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.model.form.customer.CustomerLoginFrom;
import com.atguigu.daijia.model.form.customer.UpdateCustomerPhoneForm;
import com.atguigu.daijia.model.form.customer.UpdateWxPhoneForm;
import com.atguigu.daijia.model.vo.customer.CustomerLoginVo;

public interface CustomerService {


    String login(CustomerLoginFrom customerLoginFrom);


    CustomerLoginVo getCustomerLoginInfo(Long customerId);

    Boolean updateWxPhoneNumber(UpdateWxPhoneForm updateWxPhoneForm);

    Result sendVerificationCode(String phone);

    Boolean manualBindPhone(UpdateCustomerPhoneForm updateCustomerPhoneForm);
}

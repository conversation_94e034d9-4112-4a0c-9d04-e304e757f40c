package com.atguigu.daijia.customer.service.impl;

import com.atguigu.daijia.common.constant.RedisConstant;
import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.customer.client.CustomerInfoFeignClient;
import com.atguigu.daijia.customer.service.CustomerService;
import com.atguigu.daijia.model.form.customer.CustomerLoginFrom;
import com.atguigu.daijia.model.form.customer.UpdateCustomerPhoneForm;
import com.atguigu.daijia.model.form.customer.UpdateWxPhoneForm;
import com.atguigu.daijia.model.vo.customer.CustomerLoginVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private CustomerInfoFeignClient customerInfoFeignClient;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public String login(CustomerLoginFrom customerLoginFrom) {
        // 1.拿着code值，调用微信的接口，获取到微信用户的openid
        Result<Long> loginResult = customerInfoFeignClient.login(customerLoginFrom);
        // 2.判断如果返回失败了，获取失败，返回登录失败
        Integer codeResult = loginResult.getCode();
        if (codeResult != 200){
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        }
        // 3.获取远程调用返回用户id
        Long userId = loginResult.getData();
        // 4.判断用户id是否为空，如果为空，返回错误信息
        if (userId == null){
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        }
        //5.生成token
        String token = UUID.randomUUID().toString().replaceAll("-", "");
        // 6.把用户id放到redis里面，设置过期时间
        redisTemplate.opsForValue().set(RedisConstant.USER_LOGIN_KEY_PREFIX+token,
                userId.toString(), RedisConstant.USER_LOGIN_KEY_TIMEOUT, TimeUnit.SECONDS);
        // 7返回token
        return token;
    }

    /**
     * 获取用户登录信息
     * @param customerId
     * @return
     */
    @Override
    public CustomerLoginVo getCustomerLoginInfo(Long customerId) {
        Result<CustomerLoginVo> result = customerInfoFeignClient.getCustomerLoginInfo(customerId);
        if(result.getCode().intValue() != 200) {
            throw new GuiguException(result.getCode(), result.getMessage());
        }
        CustomerLoginVo customerLoginVo = result.getData();
        if(null == customerLoginVo) {
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        }
        return customerLoginVo;
    }

    /**
     * 更新用户微信手机号码
    * @Param  [updateWxPhoneForm]
    * @return  java.lang.Boolean
    */
    @Override
    public Boolean updateWxPhoneNumber(UpdateWxPhoneForm updateWxPhoneForm) {
        Result<Boolean> booleanResult = customerInfoFeignClient.updateWxPhoneNumber(updateWxPhoneForm);
        return true;
    }
    /**
     * 发送验证码
    * @Param  [phone]
    * @return  com.atguigu.daijia.common.result.Result
    */
    @Override
    public Result sendVerificationCode(String phone) {
        return customerInfoFeignClient.sendVerificationCode(phone);
    }
    /**
     * 手动绑定手机号
    * @Param  [updateCustomerPhoneForm]
    * @return  java.lang.Boolean
    */
    @Override
    public Boolean manualBindPhone(UpdateCustomerPhoneForm updateCustomerPhoneForm) {
        return customerInfoFeignClient.manualBindPhone(updateCustomerPhoneForm).getData();
    }
}

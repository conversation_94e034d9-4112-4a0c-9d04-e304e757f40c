package com.atguigu.daijia.customer.controller;

import com.atguigu.daijia.common.constant.RedisConstant;
import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.login.GuiguLogin;
import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.common.util.AuthContextHolder;
import com.atguigu.daijia.customer.service.CustomerService;
import com.atguigu.daijia.model.form.customer.CustomerLoginFrom;
import com.atguigu.daijia.model.form.customer.UpdateCustomerPhoneForm;
import com.atguigu.daijia.model.form.customer.UpdateWxPhoneForm;
import com.atguigu.daijia.model.vo.customer.CustomerLoginVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "客户API接口管理")
@RestController
@RequestMapping("/customer")
@SuppressWarnings({"unchecked", "rawtypes"})
public class CustomerController {
    @Autowired
    private CustomerService customerService;
    @Autowired
    private RedisTemplate redisTemplate;
    /**
     * @Description  微信小程序登录接口
    */
    @Operation(summary = "微信小程序登录接口")
    @PostMapping("/login")
    public Result<String> login(@RequestBody CustomerLoginFrom customerLoginFrom)
    {
        return Result.ok(customerService.login(customerLoginFrom));
    }

    /**
     * @Description  获取客户登录信息
     */
    @Operation(summary = "获取客户登录信息")
    @GuiguLogin
    @GetMapping("/getCustomerLoginInfo")
    public Result<CustomerLoginVo> getCustomerLoginInfo() {
        // 1.从ThreadLocal获取用户id
        Long customerId = AuthContextHolder.getUserId();

        return Result.ok(customerService.getCustomerLoginInfo(customerId));
    }

    @Operation(summary = "一键获取用户微信手机号")
    @GuiguLogin
    @PostMapping("/updateWxPhone")
    public Result updateWxPhone(@RequestBody UpdateWxPhoneForm updateWxPhoneForm) {
        updateWxPhoneForm.setCustomerId(AuthContextHolder.getUserId());
        return Result.ok(customerService.updateWxPhoneNumber(updateWxPhoneForm));
    }

    @Operation(summary = "发送验证码")
    @GuiguLogin
    @GetMapping("/sendVerificationCode/{phone}")
    public Result sendVerificationCode(@PathVariable String phone){
        return Result.ok(customerService.sendVerificationCode(phone));
    }

    @Operation(summary = "手动绑定手机号")
    @GuiguLogin
    @PostMapping("/manualBindPhone")
    public Result<Boolean> manualBindPhone(@RequestBody UpdateCustomerPhoneForm updateCustomerPhoneForm) {
        updateCustomerPhoneForm.setCustomerId(AuthContextHolder.getUserId());
        return Result.ok(customerService.manualBindPhone(updateCustomerPhoneForm));
    }
}




//package com.atguigu.daijia.security.fillter;
//
//import com.atguigu.daijia.common.util.AuthContextHolder;
//import jakarta.servlet.*;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//import java.io.IOException;
//
//@Component
//@Order(Integer.MIN_VALUE) // 确保最先执行
//public class AuthContextClearFilter implements Filter {
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
//            throws IOException, ServletException {
//        try {
//            chain.doFilter(request, response); // 继续执行后续过滤器/控制器
//        } finally {
//            // 确保在请求结束时清理ThreadLocal
//            AuthContextHolder.removeUserId();
//            System.out.println("ThreadLocal清理完成"); // 生产环境移除
//        }
//    }
//}

package com.atguigu.daijia.common.login;

import com.atguigu.daijia.common.constant.RedisConstant;
import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.common.util.AuthContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @date 2025/6/5
 * @description 描述
 */
@Component
@Aspect // 切面类
public class GuiguLoginAspect {
    @Autowired
    private RedisTemplate redisTemplate;
    // 环绕通知，登入判断
    // 切入点表达式：匹配 com.atguigu.daijia 包及其子包下的所有方法
    //ProceedingJoinPoint可以获取到方法的参数等
    @Around("execution(* com.atguigu.daijia.*.controller.*.*(..)) && @annotation(guiguLogin)")
    public Object login(ProceedingJoinPoint proceedingJoinPoint,GuiguLogin guiguLogin)
            throws Throwable{
        // 1 获取request
        RequestAttributes attribute= RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra=(ServletRequestAttributes) attribute;
        HttpServletRequest request=sra.getRequest();

        // 2从请求头里面获取token
        String token = request.getHeader("token");

        // 3 判断token是否为空，如果为空，返回登入异常
        if (!StringUtils.hasText(token)){
            throw new GuiguException(ResultCodeEnum.LOGIN_AUTH);
        }
        // 4 token不为空，查询redis
        String customerId = (String) redisTemplate.opsForValue()
                .get(RedisConstant.USER_LOGIN_KEY_PREFIX + token);
        // 5 查询redis里面用户id，把用户id设置到ThreadLocal里面
        if (StringUtils.hasText(customerId)){
            AuthContextHolder.setUserId(Long.parseLong(customerId));
        }
        // 6 执行方法
        return proceedingJoinPoint.proceed();
    }
}

package com.atguigu.daijia.common.util;

/**
 * @date 2025/7/11
 * @description 描述
 */
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public class WechatAESUtils {

    /**
     * 解密微信encryptedData
     * @param encryptedData 加密数据
     * @param sessionKey    AES密钥
     * @param iv           初始化向量
     * @return 解密后的JSON字符串
     */
    public static String decrypt(String encryptedData, String sessionKey, String iv) {
        try {
            // Base64解码参数
            byte[] encryptedBytes = Base64.decodeBase64(encryptedData);
            byte[] keyBytes = Base64.decodeBase64(sessionKey);
            byte[] ivBytes = Base64.decodeBase64(iv);

            // 初始化AES解密器
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            // 执行解密
            byte[] decrypted = cipher.doFinal(encryptedBytes);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }
    }
}

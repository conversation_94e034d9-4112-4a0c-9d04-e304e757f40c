package com.atguigu.daijia.common.service;


import com.alibaba.fastjson.JSON;
import com.atguigu.daijia.common.entity.GuiguCorrelationData;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RabbitService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 发送消息
    public boolean sendMessage(String exchange, String routingKey, Object message){
        rabbitTemplate.convertAndSend(exchange, routingKey, message);
        return true;
    }
    //  发送延迟消息
    public void sendDelayMessage(String exchangeProfitsharing, String routingProfitsharing, String jsonString, int profitsharingDelayTime) {
        rabbitTemplate.convertAndSend(exchangeProfitsharing, routingProfitsharing, jsonString, message -> {
            message.getMessageProperties().setDelay(profitsharingDelayTime);
            return message;
        });
    }
//    @Autowired
//    private RedisTemplate redisTemplate;
//    public boolean sendDelayMessage(String exchange, String routingKey, Object message, int delayTime) {
//        //1.创建自定义相关消息对象-包含业务数据本身，交换器名称，路由键，队列类型，延迟时间,重试次数
//        GuiguCorrelationData correlationData = new GuiguCorrelationData();
//        String uuid = "mq:" + UUID.randomUUID().toString().replaceAll("-", "");
//        correlationData.setId(uuid);
//        correlationData.setMessage(message);
//        correlationData.setExchange(exchange);
//        correlationData.setRoutingKey(routingKey);
//        correlationData.setDelay(true);
//        correlationData.setDelayTime(delayTime);
//        //2.将相关消息封装到发送消息方法中
//        rabbitTemplate.convertAndSend(exchange, routingKey, message,message1 -> {
//            message1.getMessageProperties().setDelay(delayTime*1000);
//            return message1;
//        }, correlationData);
//        //3.将相关消息存入Redis  Key：UUID  相关消息对象  10 分钟
//        redisTemplate.opsForValue().set(uuid, JSON.toJSONString(correlationData), 10, TimeUnit.MINUTES);
//        return true;
//    }
//
//    @PostConstruct
//    public void setupConfirmCallback() {
//        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
//            if (correlationData instanceof GuiguCorrelationData) {
//                String msgId = correlationData.getId();
//                if (ack) {
//                    redisTemplate.delete(msgId);
//                    log.info("消息确认成功: {}", msgId);
//                } else {
//                    log.error("消息发送失败 ID: {}, 原因: {}", msgId, cause);
//                    // 可添加重发逻辑
//                }
//            }
//        });
//    }
}

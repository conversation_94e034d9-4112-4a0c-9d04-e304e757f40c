package com.atguigu.daijia.order.receiver;

import com.atguigu.daijia.common.constant.MqConst;
import com.atguigu.daijia.order.service.OrderInfoService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @date 2025/7/2
 * @description 描述
 */
@Component
public class OrderReceiver {
    @Autowired
    private OrderInfoService orderInfoService;
    /**
     * 订单分账成功，更新分账状态
     *
     * @param orderNo
     * @throws IOException
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = MqConst.QUEUE_PROFITSHARING_SUCCESS, durable = "true"),
            exchange = @Exchange(value = MqConst.EXCHANGE_ORDER),
            key = {MqConst.ROUTING_PROFITSHARING_SUCCESS}
    ))
    public void profitsharingSuccess(String orderNo, Message message, Channel channel) throws IOException {
        orderInfoService.updateProfitsharingStatus(orderNo);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    /**
     * TODO订单超时自动取消，已经实现了redisson里面的延迟队列，可以修改一下
     */
//    @RabbitListener(bindings = @QueueBinding(
//            value = @Queue(value = MqConst.QUEUE_ORDER_CANCEL, durable = "true"),
//            exchange = @Exchange(value = MqConst.EXCHANGE_ORDER,
//                    delayed = "true", type = ExchangeTypes.TOPIC),
//            key = {MqConst.ROUTING_ORDER_CANCEL}
//    ))
//    public void orderCancel(String orderId, Message message, Channel channel)
//            throws IOException {
//        // 处理订单取消
//        orderInfoService.orderCancel(Long.parseLong(orderId));
//        // 手动确认消息
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//    }
}

package com.atguigu.daijia.order.handle;

import com.atguigu.daijia.order.service.OrderInfoService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @date 2025/6/27
 * @description 描述
 */

@Slf4j
@Component
public class RedisDelayHandle {
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private OrderInfoService orderInfoService;


    @PostConstruct
    public void listener(){
        new Thread(()->{
            while(true){
                // 获取延迟队列的阻塞队列
                RBlockingQueue<String> blockingQueue =
                        redissonClient.getBlockingQueue("queue_cancel");
                // 从队列获取消息
                try {
                    // 会阻塞线程直到有消息到来
                    String orderId = blockingQueue.take();

                    // 处理有效订单ID
                    if(StringUtils.hasText(orderId)){
                        // 调用方法 取消订单
                        orderInfoService.orderCancel(Long.parseLong(orderId));
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();

    }
}

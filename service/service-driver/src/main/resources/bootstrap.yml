spring:
  application:
    name: service-driver  # 应用名称，用于服务注册和配置分组
  profiles:
    active: dev  # 激活的环境配置（开发环境）
  main:
    allow-bean-definition-overriding: true  # 允许 Bean 定义覆盖（避免重复 Bean 定义冲突）
  cloud:
    nacos:
      discovery:  # Nacos 服务注册配置
        server-addr: 192.168.40.123:8848  # Nacos 服务器地址
        namespace: 6376d8c3-f3dc-41fe-8774-b34d854608a1  # 命名空间 ID（用于环境隔离）
      config:  # Nacos 配置中心配置
        server-addr: 192.168.40.123:8848  # Nacos 服务器地址
        namespace: 6376d8c3-f3dc-41fe-8774-b34d854608a1  # 命名空间 ID
        prefix: ${spring.application.name}  # 配置文件前缀（默认为应用名）
        file-extension: yaml  # 配置文件扩展名
        shared-configs:  # 共享配置列表
          - data-id: common-account.yaml  # 共享配置的 Data ID

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.daijia.driver.mapper.DriverAccountDetailMapper">

	<resultMap id="RechargeInfoMap" type="com.atguigu.daijia.model.entity.driver.DriverAccountDetail" autoMapping="true">
	</resultMap>

	<sql id="columns">
		id,driver_id,title,trade_type,amount,order_no,create_time,update_time,is_deleted
	</sql>

</mapper>


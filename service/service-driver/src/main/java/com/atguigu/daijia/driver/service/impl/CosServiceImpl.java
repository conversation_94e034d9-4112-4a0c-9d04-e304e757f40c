package com.atguigu.daijia.driver.service.impl;

import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.driver.config.TencentCloudProperties;
import com.atguigu.daijia.driver.service.CiService;
import com.atguigu.daijia.driver.service.CosService;
import com.atguigu.daijia.model.vo.driver.CosUploadVo;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.utils.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class CosServiceImpl implements CosService {

    @Autowired
    private TencentCloudProperties tencentcloudProperties;

    @Autowired
    private CiService ciService;
    @Override
    public CosUploadVo upload(MultipartFile file, String path) {
        COSClient cosClient = this.getPrivateCOSClient();

        // 文件上传
        //元数据信息
        ObjectMetadata meta = new ObjectMetadata();
        meta.setContentLength(file.getSize());
        meta.setContentEncoding("UTF-8");
        meta.setContentType(file.getContentType());

        //向存储桶中保存文件
        String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")); //文件后缀名
        String uploadPath = "/driver/" + path + "/" + UUID.randomUUID().toString().replaceAll("-", "") + fileType;
        PutObjectRequest putObjectRequest = null;
        try {
            putObjectRequest = new PutObjectRequest(tencentcloudProperties.getBucketPrivate(),
                    uploadPath, file.getInputStream(), meta);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        putObjectRequest.setStorageClass(StorageClass.Standard);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest); //上传文件
        cosClient.shutdown();
        //审核图片
        Boolean isAuditing = ciService.imageAuditing(uploadPath);
        if(!isAuditing) {
            //删除违规图片
            cosClient.deleteObject(tencentcloudProperties.getBucketPrivate(), uploadPath);
            throw new GuiguException(ResultCodeEnum.IMAGE_AUDITION_FAIL);
        }
        //封装返回对象
        CosUploadVo cosUploadVo = new CosUploadVo();
        cosUploadVo.setUrl(uploadPath);
        //图片临时访问url，回显使用
        cosUploadVo.setShowUrl(this.getImageUrl(uploadPath));
        return cosUploadVo;
    }

    private COSClient getPrivateCOSClient() {
        //1初始化用户身份信息(secretId，secretKey)
        String secretId=tencentcloudProperties.getSecretId();
        String secretKey =tencentcloudProperties.getSecretKey();
        COSCredentials cred=new BasicCOSCredentials(secretId,secretKey);
        // 2设置Bucket名称，cos所属地区
        Region region=new Region(tencentcloudProperties.getRegion());
        ClientConfig clientConfig=new ClientConfig(region);
        // 3使用https协议
        clientConfig.setHttpProtocol(HttpProtocol.https);
        // 4生成cos客户端
        COSClient cosClient=new COSClient(cred,clientConfig);
        return cosClient;
    }
    // 获取图片临时访问url
    @Override
    public String getImageUrl(String path) {
        if(!StringUtils.hasText(path)) return "";
        COSClient cosClient = this.getPrivateCOSClient();
        GeneratePresignedUrlRequest request =
                new GeneratePresignedUrlRequest(tencentcloudProperties.getBucketPrivate(), path, HttpMethodName.GET);
        Date expiration = new DateTime().plusMinutes(15).toDate();
        request.setExpiration(expiration);
        URL url = cosClient.generatePresignedUrl(request);
        cosClient.shutdown();
        return url.toString();
    }

    // 获取图片base64
    @Override
    public String getImageBase64FromUrl(String imageUrl) {
        COSClient cosClient = null;
        try {
            // 1. 从配置获取密钥和区域
            String secretId = tencentcloudProperties.getSecretId();
            String secretKey = tencentcloudProperties.getSecretKey();
            String region = tencentcloudProperties.getRegion(); // 例如 "ap-guangzhou"

            // 2. 创建凭证对象
            COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);

            // 3. 创建客户端配置
            ClientConfig clientConfig = new ClientConfig(new Region(region));
            clientConfig.setHttpProtocol(HttpProtocol.https); // 使用HTTPS

            // 4. 初始化COS客户端
            cosClient = new COSClient(cred, clientConfig);

            // 从URL中提取对象键
            String objectKey = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);

            // 获取对象
            GetObjectRequest getObjectRequest = new GetObjectRequest(
                    tencentcloudProperties.getBucketPrivate(), objectKey);
            COSObject cosObject = cosClient.getObject(getObjectRequest);

            // 读取图片数据
            InputStream inputStream = cosObject.getObjectContent();
            byte[] imageBytes = IOUtils.toByteArray(inputStream);

            // 转换为base64
            String base64 = Base64.encodeBase64String(imageBytes);

            cosClient.shutdown();
            return "data:image/jpeg;base64," + base64;

        } catch (Exception e) {
            log.error("获取图片base64失败：{}", e.getMessage());
            throw new GuiguException(ResultCodeEnum.IMAGE_AUDITION_FAIL);
        }
    }
}

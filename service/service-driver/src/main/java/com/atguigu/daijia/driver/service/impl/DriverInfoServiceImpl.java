package com.atguigu.daijia.driver.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.atguigu.daijia.common.constant.SystemConstant;
import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.common.util.WechatAESUtils;
import com.atguigu.daijia.driver.config.TencentCloudProperties;
import com.atguigu.daijia.driver.mapper.*;
import com.atguigu.daijia.driver.service.CosService;
import com.atguigu.daijia.driver.service.DriverInfoService;
import com.atguigu.daijia.model.entity.driver.*;
import com.atguigu.daijia.model.form.driver.DriverFaceModelForm;
import com.atguigu.daijia.model.form.driver.DriverLoginFrom;
import com.atguigu.daijia.model.form.driver.UpdateDriverAuthInfoForm;
import com.atguigu.daijia.model.vo.driver.DriverAuthInfoVo;
import com.atguigu.daijia.model.vo.driver.DriverInfoVo;
import com.atguigu.daijia.model.vo.driver.DriverLoginVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.iai.v20180301.IaiClient;
import com.tencentcloudapi.iai.v20180301.models.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.RandomStringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class DriverInfoServiceImpl extends ServiceImpl<DriverInfoMapper, DriverInfo> implements DriverInfoService {

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private DriverInfoMapper driverInfoMapper;

    @Autowired
    private DriverSetMapper driverSetMapper;

    @Autowired
    private DriverAccountMapper driverAccountMapper;

    @Autowired
    private DriverLoginLogMapper driverLoginLogMapper;

    @Autowired
    private CosService cosService;

    @Autowired
    private TencentCloudProperties tencentCloudProperties;

    @Autowired
    private DriverFaceRecognitionMapper driverFaceRecognitionMapper;


    // 微信小程序登录
    @Override
    public Long login(DriverLoginFrom driverLoginFrom) {
        try {
            // 1. 获取微信会话信息（包含openid和sessionKey）
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(driverLoginFrom.getCode());
            String openId = sessionInfo.getOpenid();
            String sessionKey = sessionInfo.getSessionKey();
            // 2. 判断是否首次登录
            LambdaQueryWrapper<DriverInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DriverInfo::getWxOpenId, openId);
            DriverInfo driverInfo = driverInfoMapper.selectOne(wrapper);
            // 3. 解密用户信息获取头像和昵称
            String avatarUrl = null;
            String nickname = null;
            if (org.apache.commons.lang.StringUtils.isNotBlank(driverLoginFrom.getEncryptedData()) &&
                    org.apache.commons.lang.StringUtils.isNotBlank(driverLoginFrom.getIv())) {

                // 先尝试官方工具包
                try {
                    WxMaUserInfo userInfo = wxMaService.getUserService().getUserInfo(
                            sessionKey,
                            driverLoginFrom.getEncryptedData(),
                            driverLoginFrom.getIv()
                    );
                    avatarUrl = userInfo.getAvatarUrl();
                    nickname = userInfo.getNickName();
                } catch (Exception e) {
                    try {
                        String decryptedData = WechatAESUtils.decrypt(
                                driverLoginFrom.getEncryptedData(),
                                sessionKey,
                                driverLoginFrom.getIv()
                        );
                        ObjectMapper objectMapper = new ObjectMapper();
                        JsonNode root = objectMapper.readTree(decryptedData);

                        // 尝试多种可能的字段名
                        avatarUrl = root.path("avatarUrl").asText();
                        if (org.apache.commons.lang.StringUtils.isBlank(avatarUrl)) {
                            avatarUrl = root.path("avatar_url").asText();
                        }

                        nickname = root.path("nickName").asText();
                        if (org.apache.commons.lang.StringUtils.isBlank(nickname)) {
                            nickname = root.path("nickname").asText();
                        }
                        if (org.apache.commons.lang.StringUtils.isBlank(nickname)) {
                            nickname = root.path("nick_name").asText();
                        }

                    } catch (Exception ex) {
                        log.error("自定义AES解密失败", ex);
                    }
                }
            } else {
                log.info("encryptedData或iv为空，跳过解密");
            }

            // 尝试从明文用户信息中获取昵称（作为备用方案）
            if (driverLoginFrom.getUserInfo() != null &&
                    (org.apache.commons.lang.StringUtils.isBlank(nickname) || "微信用户".equals(nickname))) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    String userInfoJson = objectMapper.writeValueAsString(driverLoginFrom.getUserInfo());
                    JsonNode userInfoNode = objectMapper.readTree(userInfoJson);

                    String plainNickname = userInfoNode.path("nickName").asText();
                    String plainAvatarUrl = userInfoNode.path("avatarUrl").asText();


                    // 如果明文昵称不是"微信用户"，则使用明文昵称
                    if (org.apache.commons.lang.StringUtils.isNotBlank(plainNickname) &&
                            !"微信用户".equals(plainNickname)) {
                        nickname = plainNickname;
                    }

                    // 如果没有头像或头像是默认头像，尝试使用明文头像
                    if (org.apache.commons.lang.StringUtils.isBlank(avatarUrl) &&
                            org.apache.commons.lang.StringUtils.isNotBlank(plainAvatarUrl)) {
                        avatarUrl = plainAvatarUrl;
                    }

                } catch (Exception e) {
                    log.warn("解析明文用户信息失败", e);
                }
            }

            // 4. 首次登录初始化数据
            if (driverInfo == null) {
                driverInfo = new DriverInfo();
                // 设置昵称：优先使用解密得到的昵称
                if (org.apache.commons.lang.StringUtils.isNotBlank(nickname) && !"微信用户".equals(nickname)) {
                    // 使用真实的微信昵称
                    driverInfo.setNickname(nickname);
                } else {
                    // 如果是默认的"微信用户"或解密失败，使用更友好的随机昵称
                    driverInfo.setNickname("代驾司机_" + RandomStringUtils.randomAlphanumeric(6));
                }
                // 设置头像（优先使用微信头像，否则用默认图）
                driverInfo.setAvatarUrl(
                        org.apache.commons.lang.StringUtils.isNotBlank(avatarUrl)
                                ? avatarUrl
                                : "https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg"
                );
                driverInfo.setWxOpenId(openId);
                this.save(driverInfo);
                // 初始化默认设置
                DriverSet driverSet = new DriverSet();
                driverSet.setDriverId(driverInfo.getId());
                driverSet.setOrderDistance(new BigDecimal(0));//0：无限制
                driverSet.setAcceptDistance(new BigDecimal(SystemConstant.ACCEPT_DISTANCE));//默认接单范围：5公里
                driverSet.setIsAutoAccept(0);//0：否 1：是
                driverSetMapper.insert(driverSet);

                // 初始化司机账户
                DriverAccount driverAccount = new DriverAccount();
                driverAccount.setDriverId(driverInfo.getId());
                driverAccountMapper.insert(driverAccount);
            }
            // 5. 非首次登录更新头像和昵称（如果解密成功）
            else {
                boolean needUpdate = false;
                if (org.apache.commons.lang.StringUtils.isNotBlank(avatarUrl)) {
                    driverInfo.setAvatarUrl(avatarUrl);
                    needUpdate = true;
                }
                if (org.apache.commons.lang.StringUtils.isNotBlank(nickname) && !"微信用户".equals(nickname)) {
                    driverInfo.setNickname(nickname);
                    needUpdate = true;
                }
                if (needUpdate) {
                    driverInfoMapper.updateById(driverInfo);
                }
            }

            // 6. 登录日志
            DriverLoginLog driverLoginLog = new DriverLoginLog();
            driverLoginLog.setDriverId(driverInfo.getId());
            driverLoginLog.setMsg("小程序登录");
            driverLoginLogMapper.insert(driverLoginLog);

            // 返回司机id
            return driverInfo.getId();
        } catch (WxErrorException e) {
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        }
    }

    // 获取司机登录信息
    @Override
    public DriverLoginVo getDriverLoginInfo(Long driverId) {
        DriverInfo driverInfo = this.getById(driverId);
        DriverLoginVo driverLoginVo = new DriverLoginVo();
        BeanUtils.copyProperties(driverInfo, driverLoginVo);
        //是否创建人脸库人员，接单时做人脸识别判断
        Boolean isArchiveFace = StringUtils.hasText(driverInfo.getFaceModelId());
        driverLoginVo.setIsArchiveFace(isArchiveFace);
        return driverLoginVo;
    }

    // 获取司机认证信息
    @Override
    public DriverAuthInfoVo getDriverAuthInfo(Long driverId) {
        DriverInfo driverInfo = this.getById(driverId);
        DriverAuthInfoVo driverAuthInfoVo = new DriverAuthInfoVo();
        BeanUtils.copyProperties(driverInfo, driverAuthInfoVo);
        driverAuthInfoVo.setIdcardBackShowUrl(cosService.getImageUrl(driverAuthInfoVo.getIdcardBackUrl()));
        driverAuthInfoVo.setIdcardFrontShowUrl(cosService.getImageUrl(driverAuthInfoVo.getIdcardFrontUrl()));
        driverAuthInfoVo.setIdcardHandShowUrl(cosService.getImageUrl(driverAuthInfoVo.getIdcardHandUrl()));
        driverAuthInfoVo.setDriverLicenseFrontShowUrl(cosService.getImageUrl(driverAuthInfoVo.getDriverLicenseFrontUrl()));
        driverAuthInfoVo.setDriverLicenseBackShowUrl(cosService.getImageUrl(driverAuthInfoVo.getDriverLicenseBackUrl()));
        driverAuthInfoVo.setDriverLicenseHandShowUrl(cosService.getImageUrl(driverAuthInfoVo.getDriverLicenseHandUrl()));
        return driverAuthInfoVo;
    }

    //更新司机认证信息
    @Override
    public Boolean updateDriverAuthInfo(UpdateDriverAuthInfoForm updateDriverAuthInfoForm) {
        // 获取司机id
        Long driverId = updateDriverAuthInfoForm.getDriverId();

        //  更新司机信息
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setId(driverId);
        driverInfo.setAuthStatus(1);
        BeanUtils.copyProperties(updateDriverAuthInfoForm, driverInfo);
        boolean update = this.updateById(driverInfo);
        return update;
    }

    // 创建人脸模型
    @Override
    public Boolean creatDriverFaceModel(DriverFaceModelForm driverFaceModelForm) {
        DriverInfo driverInfo = this.getById(driverFaceModelForm.getDriverId());
        try {
            // todo 这里需要验证人脸识别
            // 1. 先进行人脸与身份证照片对比验证
            if (StringUtils.hasText(driverInfo.getIdcardFrontUrl())) {
                // 获取身份证照片的base64编码
                String idCardImageBase64 = cosService.getImageBase64FromUrl(driverInfo.getIdcardFrontUrl());

                // 调用腾讯云人脸对比API
                if (!compareFaceWithIdCard(driverFaceModelForm.getImageBase64(), idCardImageBase64)) {
                    throw new GuiguException(ResultCodeEnum.IMAGE_AUDITION_FAIL);
                }
            } else {
                throw new GuiguException(ResultCodeEnum.IMAGE_AUDITION_FAIL);
            }
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(tencentCloudProperties.getSecretId(), tencentCloudProperties.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            // 域名
            httpProfile.setEndpoint("iai.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            IaiClient client = new IaiClient(cred, tencentCloudProperties.getRegion(), clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            CreatePersonRequest req = new CreatePersonRequest();
            //基本信息
            req.setGroupId(tencentCloudProperties.getPersionGroupId());
            req.setPersonId(String.valueOf(driverInfo.getId()));
            req.setGender(Long.parseLong(driverInfo.getGender()));
            req.setQualityControl(4L);
            req.setUniquePersonControl(4L);
            req.setPersonName(driverInfo.getName());
            req.setImage(driverFaceModelForm.getImageBase64());

            // 返回的resp是一个CreatePersonResponse的实例，与请求对象对应
            CreatePersonResponse resp = client.CreatePerson(req);
            // 输出json格式的字符串回包
            System.out.println(CreatePersonResponse.toJsonString(resp));
            if (StringUtils.hasText(resp.getFaceId())) {
                //人脸校验必要参数，保存到数据库表
                driverInfo.setFaceModelId(resp.getFaceId());
                this.updateById(driverInfo);
            }
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云人脸识别失败，错误码：{}，错误信息：{}", e.getErrorCode(), e.getMessage());
            e.printStackTrace();
            return false;
        }
        return true;
    }

    // 人脸与身份证照片对比验证
    private Boolean compareFaceWithIdCard(String faceImageBase64, String idCardImageBase64) {
        try {
            String secretId = tencentCloudProperties.getSecretId();
            String secretKey = tencentCloudProperties.getSecretKey();
            String region = tencentCloudProperties.getRegion(); // 例如 "ap-guangzhou"
            Credential cred = new Credential(secretId, secretKey);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            IaiClient client = new IaiClient(cred, region, clientProfile);
            // 调用人脸对比API
            CompareFaceRequest req = new CompareFaceRequest();
            req.setImageA(faceImageBase64);  // 当前上传的人脸照片
            req.setImageB(idCardImageBase64); // 身份证照片
            CompareFaceResponse resp = client.CompareFace(req);
            // 相似度阈值判断（建议80分以上认为是同一人）
            Float score = resp.getScore();
            log.info("人脸对比相似度：{}", score);
            return score != null && score >= 80.0f;
        } catch (TencentCloudSDKException e) {
            log.error("人脸对比失败：{}", e.getMessage());
            return false;
        }
    }

    // 获取司机设置
    @Override
    public DriverSet getDriverSet(Long driverId) {
        LambdaQueryWrapper<DriverSet> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DriverSet::getDriverId, driverId);
        DriverSet driverSet = driverSetMapper.selectOne(wrapper);
        return driverSet;
    }

    // 判断司机当日是否进行过人脸识别
    @Override
    public Boolean isFaceRecognition(Long driverId) {
        // 根据司机id+当时日期进行查询
        LambdaQueryWrapper<DriverFaceRecognition> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(DriverFaceRecognition::getDriverId, driverId);
        queryWrapper.eq(DriverFaceRecognition::getFaceDate, new DateTime().toString("yyyy-MM-dd"));
        long count = driverFaceRecognitionMapper.selectCount(queryWrapper);
        return count != 0;
    }


    // 验证司机人脸
    @Override
    public Boolean verifyDriverFace(DriverFaceModelForm driverFaceModelForm) {
        //照片比对
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(tencentCloudProperties.getSecretId(),
                    tencentCloudProperties.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            IaiClient client = new IaiClient(cred, tencentCloudProperties.getRegion(), clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            VerifyFaceRequest req = new VerifyFaceRequest();
            req.setImage(driverFaceModelForm.getImageBase64());
            req.setPersonId(String.valueOf(driverFaceModelForm.getDriverId()));
            // 返回的resp是一个VerifyFaceResponse的实例，与请求对象对应
            VerifyFaceResponse resp = client.VerifyFace(req);
            // 输出json格式的字符串回包
            System.out.println(VerifyFaceResponse.toJsonString(resp));
            if (resp.getIsMatch()) { // 人脸匹配
                //活体检查
                if (this.detectLiveFace(driverFaceModelForm.getImageBase64())) {
                    DriverFaceRecognition driverFaceRecognition = new DriverFaceRecognition();
                    driverFaceRecognition.setDriverId(driverFaceModelForm.getDriverId());
                    driverFaceRecognition.setFaceDate(new Date());
                    driverFaceRecognitionMapper.insert(driverFaceRecognition);
                    return true;
                }
                ;
            }
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
        throw new GuiguException(ResultCodeEnum.FACE_ERROR);
    }

    // 更新司机服务状态
    @Override
    public Boolean updateServiceStatus(Long driverId, Integer status) {
        LambdaQueryWrapper<DriverSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DriverSet::getDriverId, driverId);
        DriverSet driverSet = new DriverSet();
        driverSet.setServiceStatus(status);
        driverSetMapper.update(driverSet, queryWrapper);
        return true;
    }

    // 获取司机信息
    @Override
    public DriverInfoVo getDriverInfo(Long driverId) {
        DriverInfo driverInfo = this.getById(driverId);
        DriverInfoVo driverInfoVo = new DriverInfoVo();
        BeanUtils.copyProperties(driverInfo, driverInfoVo);
        //驾龄
        Integer driverLicenseAge =
                new DateTime().getYear() - new DateTime(driverInfo.getDriverLicenseIssueDate()).getYear() + 1;
        driverInfoVo.setDriverLicenseAge(driverLicenseAge);
        return driverInfoVo;
    }

    @Override
    public String getDriverOpenId(Long driverId) {
        DriverInfo driverInfo = this.getOne(new LambdaQueryWrapper<DriverInfo>().eq(DriverInfo::getId, driverId).select(DriverInfo::getWxOpenId));
        return driverInfo.getWxOpenId();
    }

    // 更新司机设置
    @Override
    public Boolean updateDriverSet(DriverSet driverSet) {
        LambdaQueryWrapper<DriverSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DriverSet::getDriverId, driverSet.getDriverId());
        DriverSet existingDriverSet = driverSetMapper.selectOne(queryWrapper);

        if (existingDriverSet != null) {
            // 更新现有设置
            existingDriverSet.setOrderDistance(driverSet.getOrderDistance());
            existingDriverSet.setAcceptDistance(driverSet.getAcceptDistance());
            existingDriverSet.setIsAutoAccept(driverSet.getIsAutoAccept());
            return driverSetMapper.updateById(existingDriverSet) > 0;
        } else {
            // 创建新的设置记录
            return driverSetMapper.insert(driverSet) > 0;
        }
    }


    //人脸活体检测
    private Boolean detectLiveFace(String imageBase64) {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(tencentCloudProperties.getSecretId(), tencentCloudProperties.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            IaiClient client = new IaiClient(cred, tencentCloudProperties.getRegion(), clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DetectLiveFaceRequest req = new DetectLiveFaceRequest();
            req.setImage(imageBase64);
            // 返回的resp是一个DetectLiveFaceResponse的实例，与请求对象对应
            DetectLiveFaceResponse resp = client.DetectLiveFace(req);
            // 输出json格式的字符串回包
            System.out.println(DetectLiveFaceResponse.toJsonString(resp));
            if (resp.getIsLiveness()) {
                return true;
            }
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
        return false;
    }
}

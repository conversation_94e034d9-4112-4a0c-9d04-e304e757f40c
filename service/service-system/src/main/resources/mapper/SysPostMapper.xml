<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.daijia.system.mapper.SysPostMapper">

	<resultMap id="sysPostMap" type="com.atguigu.daijia.model.entity.system.SysPost" autoMapping="true">
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="columns">
		id,post_code,name,description,status,create_time,update_time,is_deleted
	</sql>

	<sql id="findPageWhere">
		<where>
	       <if test="query.postCode != null and query.postCode != ''">
				and post_code = #{query.postCode}
			</if>
	       <if test="query.name != null and query.name != ''">
				and name = #{query.name}
			</if>
	       <if test="query.status != null and query.status != ''">
				and status = #{query.status}
			</if>
			and is_deleted = 0
		</where>
	</sql>

    <select id="selectPage" resultMap="sysPostMap">
    	select <include refid="columns" />
	    from sys_post
		<include refid="findPageWhere"/>
		order by id desc
    </select>

</mapper>


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.daijia.system.mapper.SysOperLogMapper">

	<resultMap id="sysOperLogMap" type="com.atguigu.daijia.model.entity.system.SysOperLog" autoMapping="true">
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="columns">
		id,title,business_type,method,request_method,operator_type,oper_name,dept_name,oper_url,oper_ip,oper_param,json_result,status,error_msg,oper_time,create_time,update_time,is_deleted
	</sql>

	<sql id="findPageWhere">
		<where>
			<if test="query.title != null and query.title != ''">
				and title like CONCAT('%',#{query.title},'%')
			</if>
			<if test="query.operName != null and query.operName != ''">
				and oper_name like CONCAT('%',#{query.operName},'%')
			</if>
	       <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
				and create_time >= #{query.createTimeBegin}
		   </if>
		   <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
				and create_time &lt;= #{query.createTimeEnd}
		   </if>
			and is_deleted = 0
		</where>
	</sql>

    <select id="selectPage" resultMap="sysOperLogMap">
    	select <include refid="columns" />
	    from sys_oper_log
		<include refid="findPageWhere"/>
		order by id desc
    </select>

</mapper>


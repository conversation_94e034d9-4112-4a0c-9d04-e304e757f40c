<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.daijia.system.mapper.SysRoleMapper">

	<resultMap id="RoleMap" type="com.atguigu.daijia.model.entity.system.SysRole" autoMapping="true">
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="columns">
		id,role_name,role_code,description,create_time,update_time,is_deleted
	</sql>

    <select id="selectPage" resultMap="RoleMap">
    	select <include refid="columns" />
	    from sys_role
		<where>
			<if test="query.roleName != null and query.roleName != ''">
				and role_name like CONCAT('%',#{query.roleName},'%')
			</if>
			and is_deleted = 0
		</where>
		order by id desc
    </select>

</mapper>


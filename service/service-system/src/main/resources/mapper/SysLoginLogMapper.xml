<?xml version="1.0" encoding="UTF-8"?>
	<!DOCTYPE mapper
			PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
			"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.daijia.system.mapper.SysLoginLogMapper">

	<resultMap id="sysLoginLogMap" type="com.atguigu.daijia.model.entity.system.SysLoginLog" autoMapping="true">
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="columns">
		id,username,ipaddr,status,msg,access_time,create_time,update_time,is_deleted
	</sql>

	<sql id="findPageWhere">
		<where>
	       <if test="query.username != null and query.username != ''">
				and username = #{query.username}
			</if>
	       <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
				and create_time >= #{query.createTimeBegin}
		   </if>
		   <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
				and create_time &lt;= #{query.createTimeEnd}
		   </if>
			and is_deleted = 0
		</where>
	</sql>

    <select id="selectPage" resultMap="sysLoginLogMap">
    	select <include refid="columns" />
	    from sys_login_log
		<include refid="findPageWhere"/>
		order by id desc
    </select>

</mapper>


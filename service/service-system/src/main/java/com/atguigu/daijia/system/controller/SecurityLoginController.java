package com.atguigu.daijia.system.controller;

import com.alibaba.fastjson.JSON;
import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.common.util.LoginUserInfoHelper;
import com.atguigu.daijia.model.entity.system.SysUser;
import com.atguigu.daijia.model.vo.system.LoginVo;
import com.atguigu.daijia.system.service.SysMenuService;
import com.atguigu.daijia.system.service.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "security登录管理")
@RestController
@RequestMapping(value="/securityLogin")
@SuppressWarnings({"unchecked", "rawtypes"})
public class SecurityLoginController {

	@Resource
	private SysUserService sysUserService;

	@Resource
	private SysMenuService sysMenuService;

	@Operation(summary = "用户登录")
	@PostMapping("login")
	public Result login(@RequestBody LoginVo loginVo) {
		log.info("用户登录: {}", JSON.toJSONString(loginVo));
		// 根据用户名查询用户
		SysUser sysUser = sysUserService.getByUsername(loginVo.getUsername());
		if (sysUser == null) {
			return Result.fail("用户名不存在");
		}

		// 简单模拟密码验证，实际应该使用加密比对
		if (!loginVo.getPassword().equals(sysUser.getPassword())) {
			return Result.fail("密码错误");
		}

		// 登录成功，生成token
		String token = "admin-token"; // 实际应该使用JWT等方式生成token

		// 设置当前登录用户信息
		LoginUserInfoHelper.setUserId(sysUser.getId());
		LoginUserInfoHelper.setUsername(sysUser.getUsername());

		// 返回token
		Map<String, Object> map = new HashMap<>();
		map.put("token", token);
		return Result.ok(map);
	}

	@Operation(summary = "根据用户名获取用户信息")
	@GetMapping("getByUsername/{username}")
	public Result<SysUser> getByUsername(@PathVariable String username) {
		return Result.ok(sysUserService.getByUsername(username));
	}

	@Operation(summary = "获取用户按钮权限")
	@GetMapping("findUserPermsList/{userId}")
	public Result<List<String>> findUserPermsList(@PathVariable Long userId) {
		return Result.ok(sysMenuService.findUserPermsList(userId));
	}

	@Operation(summary = "获取用户信息")
	@GetMapping("getUserInfo/{userId}")
	public Result<Map<String, Object>> getUserInfo(@PathVariable Long userId) {
		if (userId == null) {
			return Result.fail("用户未登录");
		}
		Map<String, Object> map = sysUserService.getUserInfo(userId);
		return Result.ok(map);
	}
}


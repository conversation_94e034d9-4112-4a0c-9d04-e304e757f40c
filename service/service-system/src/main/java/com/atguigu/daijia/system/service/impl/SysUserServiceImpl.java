package com.atguigu.daijia.system.service.impl;

import com.atguigu.daijia.model.entity.system.SysRole;
import com.atguigu.daijia.model.entity.system.SysUser;
import com.atguigu.daijia.model.query.system.SysUserQuery;
import com.atguigu.daijia.model.vo.base.PageVo;
import com.atguigu.daijia.model.vo.system.RouterVo;
import com.atguigu.daijia.system.mapper.SysUserMapper;
import com.atguigu.daijia.system.service.SysMenuService;
import com.atguigu.daijia.system.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

	@Autowired
	private SysUserMapper sysUserMapper;

	@Autowired
	private SysMenuService sysMenuService;

	@Override
	public PageVo<SysUser> findPage(Page<SysUser> pageParam, SysUserQuery sysUserQuery) {
		IPage<SysUser> pageInfo = sysUserMapper.selectPage(pageParam, sysUserQuery);

		// 为每个用户查询角色信息
		List<SysUser> records = pageInfo.getRecords();
		for (SysUser user : records) {
			List<SysRole> roles = sysUserMapper.selectUserRoles(user.getId());
			user.setRoleList(roles);
		}
		return new PageVo(records, pageInfo.getPages(), pageInfo.getTotal());
	}

	@Transactional
	@Override
	public void updateStatus(Long id, Integer status) {
		SysUser sysUser = this.getById(id);
		if(status.intValue() == 1) {
			sysUser.setStatus(status);
		} else {
			sysUser.setStatus(0);
		}
		this.updateById(sysUser);
	}

	@Override
	public SysUser getByUsername(String username) {
		return this.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username));
	}

	@Override
	public Map<String, Object> getUserInfo(Long userId) {
		Map<String, Object> result = new HashMap<>();
		SysUser sysUser = this.getById(userId);

		if (sysUser == null) {
			// 如果用户不存在，创建一个默认的超级管理员
			sysUser = new SysUser();
			sysUser.setId(1L);
			sysUser.setUsername("admin");
			sysUser.setName("管理员");
			sysUser.setStatus(1);
		}

		//根据用户id获取菜单权限值
		List<RouterVo> routerVoList = sysMenuService.findUserMenuList(sysUser.getId());
		//根据用户id获取用户按钮权限
		List<String> permsList = sysMenuService.findUserPermsList(sysUser.getId());

		result.put("id", sysUser.getId());
		result.put("name", sysUser.getName());
		result.put("avatar", "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif");

		// 添加角色信息，确保前端权限控制正常工作
		HashSet<String> roles = new HashSet<>();
		roles.add("admin");
		result.put("roles", roles);

		// 添加权限信息
		result.put("permissions", permsList);

		// 兼容旧的API
		result.put("buttons", permsList);
		result.put("routers", routerVoList);
		return result;
	}
}

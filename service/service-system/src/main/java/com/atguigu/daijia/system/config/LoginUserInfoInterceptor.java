package com.atguigu.daijia.system.config;

import com.atguigu.daijia.common.util.LoginUserInfoHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户信息拦截器
 * 从请求头中获取用户ID和用户名，并设置到ThreadLocal中
 */
public class LoginUserInfoInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        System.out.println("=== 拦截器处理请求 ===");
        System.out.println("请求URL: " + request.getRequestURI());
        System.out.println("请求方法: " + request.getMethod());

        // 打印所有请求头
        System.out.println("所有请求头:");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            System.out.println("  " + headerName + ": " + headerValue);
        }

        // 从请求头中获取用户ID
        String userId = request.getHeader("userId");
        System.out.println("请求头中的userId: " + userId);
        if (StringUtils.hasText(userId)) {
            LoginUserInfoHelper.setUserId(Long.parseLong(userId));
        }

        // 从请求头中获取用户名
        String username = request.getHeader("username");
        System.out.println("请求头中的username: " + username);
        if (StringUtils.hasText(username)) {
            LoginUserInfoHelper.setUsername(username);
        }

        // 如果没有用户ID，默认使用超级管理员ID
        if (LoginUserInfoHelper.getUserId() == null) {
            System.out.println("用户ID为空，使用默认超级管理员ID");
            LoginUserInfoHelper.setUserId(1L);
            LoginUserInfoHelper.setUsername("admin");
        }

        System.out.println("最终设置的用户ID: " + LoginUserInfoHelper.getUserId());
        System.out.println("最终设置的用户名: " + LoginUserInfoHelper.getUsername());
        System.out.println("=== 拦截器处理完成 ===");

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求结束后，清除ThreadLocal中的数据
        LoginUserInfoHelper.removeUserId();
        LoginUserInfoHelper.removeUsername();
    }
}

package com.atguigu.daijia.system.service.impl;

import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.model.entity.system.SysMenu;
import com.atguigu.daijia.model.entity.system.SysRoleMenu;
import com.atguigu.daijia.model.vo.system.AssginMenuVo;
import com.atguigu.daijia.model.vo.system.MetaVo;
import com.atguigu.daijia.model.vo.system.RouterVo;
import com.atguigu.daijia.system.helper.MenuHelper;
import com.atguigu.daijia.system.mapper.SysMenuMapper;
import com.atguigu.daijia.system.mapper.SysRoleMenuMapper;
import com.atguigu.daijia.system.service.SysMenuService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Override
    public List<SysMenu> findNodes() {
        //全部权限列表
        List<SysMenu> sysMenuList = this.list();
        if (CollectionUtils.isEmpty(sysMenuList)) return null;

        //构建树形数据
        List<SysMenu> result = MenuHelper.buildTree(sysMenuList);
        return result;
    }

    @Override
    public boolean removeById(Serializable id) {
        long count = this.count(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getParentId, id));
        if (count > 0) {
            throw new GuiguException(ResultCodeEnum.NODE_ERROR);
        }
        sysMenuMapper.deleteById(id);
        return false;
    }

    @Override
    public List<SysMenu> findSysMenuByRoleId(Long roleId) {
        //全部权限列表（只获取状态为正常的菜单）
        List<SysMenu> allSysMenuList = this.list(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getStatus, 1).orderByAsc(SysMenu::getSortValue));

        //根据角色id获取角色权限
        List<SysRoleMenu> sysRoleMenuList = sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, roleId));
        //转换给角色id与角色权限对应Map对象
        List<Long> menuIdList = sysRoleMenuList.stream().map(e -> e.getMenuId()).collect(Collectors.toList());

        // 设置菜单的选中状态
        allSysMenuList.forEach(permission -> {
            if (menuIdList.contains(permission.getId())) {
                permission.setSelect(true);
            } else {
                permission.setSelect(false);
            }
        });

        // 构建树形结构
        List<SysMenu> sysMenuList = MenuHelper.buildTree(allSysMenuList);
        return sysMenuList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doAssign(AssginMenuVo assginMenuVo) {
        // 删除原有的角色菜单关联
        sysRoleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, assginMenuVo.getRoleId()));

        // 获取菜单ID列表
        List<Long> menuIdList = assginMenuVo.getMenuIdList();

        // 如果菜单ID列表为空，则直接返回，表示清空角色的所有菜单权限
        if (CollectionUtils.isEmpty(menuIdList)) {
            return;
        }

        // 批量添加新的角色菜单关联
        for (Long menuId : menuIdList) {
            if (null == menuId) continue;
            SysRoleMenu roleMenu = new SysRoleMenu();
            roleMenu.setRoleId(assginMenuVo.getRoleId());
            roleMenu.setMenuId(menuId);
            sysRoleMenuMapper.insert(roleMenu);
        }
    }

    @Override
    public List<RouterVo> findUserMenuList(Long userId) {
        String requestId = "SERVICE-" + System.currentTimeMillis();
        System.out.println("=== Service获取用户菜单列表 [" + requestId + "] ===");
        System.out.println("[" + requestId + "] 用户ID: " + userId);

        //超级管理员admin账号id为：1
        List<SysMenu> sysMenuList = null;
        if (userId.longValue() == 1) {
            System.out.println("[" + requestId + "] 超级管理员，获取所有菜单");
            sysMenuList = this.list(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getStatus, 1).orderByAsc(SysMenu::getSortValue));
        } else {
            System.out.println("[" + requestId + "] 普通用户，根据角色权限获取菜单");

            // 调试：查看用户的角色分配
            System.out.println("[" + requestId + "] 调试：检查用户角色分配");
            // 这里可以添加查询用户角色的代码，但为了不修改其他业务代码，我们先看菜单结果

            sysMenuList = sysMenuMapper.findListByUserId(userId);

            // 添加调试：检查SQL查询结果
            System.out.println("[" + requestId + "] SQL查询完成，结果数量: " + (sysMenuList != null ? sysMenuList.size() : 0));

            // 统计各个模块的菜单数量
            if (sysMenuList != null && sysMenuList.size() > 0) {
                System.out.println("[" + requestId + "] 用户权限分析：");
                long systemMenus = sysMenuList.stream().filter(m -> m.getName().contains("系统") || m.getId() == 2L || (m.getParentId() != null && m.getParentId() == 2L)).count();
                long albumMenus = sysMenuList.stream().filter(m -> m.getName().contains("专辑") || m.getId() == 45L || (m.getParentId() != null && m.getParentId() == 45L)).count();
                long orderMenus = sysMenuList.stream().filter(m -> m.getName().contains("订单") || m.getId() == 50L || (m.getParentId() != null && m.getParentId() == 50L)).count();
                long memberMenus = sysMenuList.stream().filter(m -> m.getName().contains("会员") || m.getId() == 53L || (m.getParentId() != null && m.getParentId() == 53L)).count();
                long driverMenus = sysMenuList.stream().filter(m -> m.getName().contains("司机") || m.getId() == 67L || (m.getParentId() != null && m.getParentId() == 67L)).count();
                long customerMenus = sysMenuList.stream().filter(m -> m.getName().contains("乘客") || m.getId() == 69L || (m.getParentId() != null && m.getParentId() == 69L)).count();

                System.out.println("[" + requestId + "] - 系统管理相关菜单: " + systemMenus + " 个");
                System.out.println("[" + requestId + "] - 专辑管理相关菜单: " + albumMenus + " 个");
                System.out.println("[" + requestId + "] - 订单管理相关菜单: " + orderMenus + " 个");
                System.out.println("[" + requestId + "] - 会员管理相关菜单: " + memberMenus + " 个");
                System.out.println("[" + requestId + "] - 司机端相关菜单: " + driverMenus + " 个");
                System.out.println("[" + requestId + "] - 乘客端相关菜单: " + customerMenus + " 个");
                System.out.println("[" + requestId + "] 问题：用户应该只有系统管理权限，但却获得了所有模块权限！");
            } else {
                System.out.println("[" + requestId + "] 警告：用户ID " + userId + " 没有查询到任何菜单权限！");
            }
        }

        //构建树形数据
        System.out.println("[" + requestId + "] 准备构建树形结构，输入菜单数量: " + (sysMenuList != null ? sysMenuList.size() : 0));
        if (sysMenuList != null && sysMenuList.size() > 0) {
            System.out.println("[" + requestId + "] 输入到buildTree的菜单列表:");
            for (SysMenu menu : sysMenuList) {
                System.out.println("[" + requestId + "] 输入菜单: " + menu.getName() + " (ID: " + menu.getId() + ", 父ID: " + menu.getParentId() + ", 类型: " + menu.getType() + ")");
            }
        }

        List<SysMenu> sysMenuTreeList = MenuHelper.buildTree(sysMenuList);
        System.out.println("[" + requestId + "] 构建树形结构后的菜单数量: " + (sysMenuTreeList != null ? sysMenuTreeList.size() : 0));

        List<RouterVo> routerVoList = this.buildMenus(sysMenuTreeList);
        System.out.println("[" + requestId + "] Service最终返回的路由数量: " + routerVoList.size());

        // 打印Service返回的路由详情
        if (routerVoList != null && routerVoList.size() > 0) {
            System.out.println("[" + requestId + "] Service返回的路由详情:");
            for (int i = 0; i < routerVoList.size(); i++) {
                RouterVo router = routerVoList.get(i);
                System.out.println("[" + requestId + "] Service路由" + (i+1) + ": " + router.getPath() + " - " + (router.getMeta() != null ? router.getMeta().getTitle() : "无标题"));
            }
        }

        System.out.println("[" + requestId + "] === Service结束获取用户菜单列表 ===");
        return routerVoList;
    }

    /**
     * 根据菜单构建路由
     * @param menus 菜单列表
     * @return 路由列表
     */
    private List<RouterVo> buildMenus(List<SysMenu> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenu menu : menus) {
            // 只处理状态为正常(1)的菜单
            if (menu.getStatus() == null || menu.getStatus() != 1) {
                continue;
            }

            RouterVo router = new RouterVo();
            router.setAlwaysShow(false);
            router.setPath(getRouterPath(menu));
            router.setComponent(menu.getComponent());
            Boolean isHide = menu.getIsHide() != null && menu.getIsHide().intValue() == 1;
            router.setMeta(new MetaVo(menu.getName(), menu.getIcon(), menu.getActiveMenu(), isHide));

            List<SysMenu> children = menu.getChildren();

            // 根据菜单类型处理
            if (menu.getType() != null) {
                // 如果当前是菜单(type=1)，需将按钮对应的路由加载出来
                if (menu.getType().intValue() == 1) {
                    // 过滤有组件路径的子菜单（通常是按钮对应的页面）
                    List<SysMenu> hiddenMenuList = children.stream()
                        .filter(item -> item.getStatus() != null && item.getStatus() == 1) // 只处理状态正常的
                        .filter(item -> StringUtils.hasText(item.getComponent()))
                        .collect(Collectors.toList());

                    for (SysMenu hiddenMenu : hiddenMenuList) {
                        RouterVo hiddenRouter = new RouterVo();
                        hiddenRouter.setAlwaysShow(false);
                        hiddenRouter.setPath(getRouterPath(hiddenMenu));
                        hiddenRouter.setComponent(hiddenMenu.getComponent());
                        Boolean isHide1 = hiddenMenu.getIsHide() != null && hiddenMenu.getIsHide().intValue() == 1;
                        hiddenRouter.setMeta(new MetaVo(hiddenMenu.getName(), hiddenMenu.getIcon(), menu.getActiveMenu(), isHide1));
                        routers.add(hiddenRouter);
                    }
                }
                // 如果是目录(type=0)，则递归处理子菜单
                else if (menu.getType().intValue() == 0) {
                    if (!CollectionUtils.isEmpty(children)) {
                        if (children.size() > 0) {
                            router.setAlwaysShow(true);
                        }
                        // 递归构建子菜单的路由
                        router.setChildren(buildMenus(children));
                    }
                }
                // 按钮(type=2)不需要生成路由
            }

            routers.add(router);
        }
        return routers;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu) {
        String routerPath = "/" + menu.getPath();
        if(menu.getParentId().intValue() != 0) {
            routerPath = menu.getPath();
        }
        return routerPath;
    }

    @Override
    public List<String> findUserPermsList(Long userId) {
        //超级管理员admin账号id为：1
        List<SysMenu> sysMenuList = null;
        if (userId.longValue() == 1) {
            sysMenuList = this.list(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getStatus, 1));
        } else {
            sysMenuList = sysMenuMapper.findListByUserId(userId);
        }
        // 过滤类型为2(按钮)的菜单，并获取其权限标识
        List<String> permsList = sysMenuList.stream()
            .filter(item -> item.getType() == 2) // 2:按钮
            .filter(item -> StringUtils.hasText(item.getPerms())) // 确保权限标识不为空
            .map(item -> item.getPerms())
            .collect(Collectors.toList());
        return permsList;
    }
}

package com.atguigu.daijia.system.service;

import com.atguigu.daijia.model.entity.system.SysRole;
import com.atguigu.daijia.model.query.system.SysRoleQuery;
import com.atguigu.daijia.model.vo.base.PageVo;
import com.atguigu.daijia.model.vo.system.AssginRoleVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

public interface SysRoleService extends IService<SysRole> {

    PageVo<SysRole> findPage(Page<SysRole> pageParam, SysRoleQuery roleQuery);

    /**
     * 根据用户获取角色数据
     * @param userId
     * @return
     */
    Map<String, Object> findRoleByUserId(Long userId);

    /**
     * 分配角色
     * @param assginRoleVo
     */
    void doAssign(AssginRoleVo assginRoleVo);

    /**
     * 批量删除角色
     * @param idList
     */
    void batchRemove(java.util.List<Long> idList);

    /**
     * 根据角色编码查询角色
     * @param roleCode
     * @return
     */
    SysRole getByRoleCode(String roleCode);
}

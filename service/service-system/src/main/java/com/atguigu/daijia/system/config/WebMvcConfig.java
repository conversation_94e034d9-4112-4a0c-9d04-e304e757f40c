package com.atguigu.daijia.system.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册用户信息拦截器，拦截所有请求
        registry.addInterceptor(new LoginUserInfoInterceptor())
                .addPathPatterns("/**");
    }
}
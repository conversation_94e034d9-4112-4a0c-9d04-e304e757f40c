package com.atguigu.daijia.system.service.impl;

import com.atguigu.daijia.model.entity.system.SysRole;
import com.atguigu.daijia.model.entity.system.SysUserRole;
import com.atguigu.daijia.model.query.system.SysRoleQuery;
import com.atguigu.daijia.model.vo.base.PageVo;
import com.atguigu.daijia.model.vo.system.AssginRoleVo;
import com.atguigu.daijia.system.mapper.SysRoleMapper;
import com.atguigu.daijia.system.mapper.SysUserRoleMapper;
import com.atguigu.daijia.system.service.SysRoleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public PageVo<SysRole> findPage(Page<SysRole> pageParam, SysRoleQuery roleQuery) {
        IPage<SysRole> pageInfo = sysRoleMapper.selectPage(pageParam, roleQuery);
        return new PageVo(pageInfo.getRecords(), pageInfo.getPages(), pageInfo.getTotal());
    }

    @Override
    public Map<String, Object> findRoleByUserId(Long userId) {
        //查询所有的角色
        List<SysRole> allRolesList = this.list();

        //拥有的角色id
        List<SysUserRole> existUserRoleList = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId).select(SysUserRole::getRoleId));
        List<Long> existRoleIdList = existUserRoleList.stream().map(c->c.getRoleId()).collect(Collectors.toList());

        //对角色进行分类
        List<SysRole> assginRoleList = new ArrayList<>();
        for (SysRole role : allRolesList) {
            //已分配
            if(existRoleIdList.contains(role.getId())) {
                assginRoleList.add(role);
            }
        }

        Map<String, Object> roleMap = new HashMap<>();
        roleMap.put("assginRoleList", assginRoleList);
        roleMap.put("allRolesList", allRolesList);
        return roleMap;
    }

    @Transactional
    @Override
    public void doAssign(AssginRoleVo assginRoleVo) {
        sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, assginRoleVo.getUserId()));

        for(Long roleId : assginRoleVo.getRoleIdList()) {
            if(null == roleId) continue;
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(assginRoleVo.getUserId());
            userRole.setRoleId(roleId);
            sysUserRoleMapper.insert(userRole);
        }
    }

    @Transactional
    @Override
    public void batchRemove(List<Long> idList) {
        // 检查角色是否被用户使用
        for (Long roleId : idList) {
            long count = sysUserRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, roleId));
            if (count > 0) {
                SysRole role = this.getById(roleId);
                throw new RuntimeException("角色【" + role.getRoleName() + "】已被用户使用，不能删除");
            }
        }
        this.removeByIds(idList);
    }

    @Override
    public SysRole getByRoleCode(String roleCode) {
        return this.getOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, roleCode));
    }

    @Override
    public boolean save(SysRole entity) {
        // 检查角色编码是否重复
        if (entity.getRoleCode() != null) {
            long count = this.count(new LambdaQueryWrapper<SysRole>()
                    .eq(SysRole::getRoleCode, entity.getRoleCode()));
            if (count > 0) {
                throw new RuntimeException("角色编码【" + entity.getRoleCode() + "】已存在");
            }
        }
        return super.save(entity);
    }

    @Override
    public boolean updateById(SysRole entity) {
        // 检查角色编码是否重复（排除自己）
        if (entity.getRoleCode() != null) {
            long count = this.count(new LambdaQueryWrapper<SysRole>()
                    .eq(SysRole::getRoleCode, entity.getRoleCode())
                    .ne(SysRole::getId, entity.getId()));
            if (count > 0) {
                throw new RuntimeException("角色编码【" + entity.getRoleCode() + "】已存在");
            }
        }
        return super.updateById(entity);
    }

    @Override
    public boolean removeById(Serializable id) {
        // 检查角色是否被用户使用
        long count = sysUserRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, id));
        if (count > 0) {
            SysRole role = this.getById(id);
            throw new RuntimeException("角色【" + role.getRoleName() + "】已被用户使用，不能删除");
        }
        return super.removeById(id);
    }
}

package com.atguigu.daijia.system.controller;

import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.common.util.LoginUserInfoHelper;
import com.atguigu.daijia.model.entity.system.SysMenu;
import com.atguigu.daijia.model.vo.system.AssginMenuVo;
import com.atguigu.daijia.model.vo.system.RouterVo;
import com.atguigu.daijia.system.service.SysMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "菜单管理")
@RestController
@RequestMapping("/sysMenu")
public class SysMenuController {

    @Autowired
    private SysMenuService sysMenuService;

    @Operation(summary = "获取用户菜单列表")
    @GetMapping("getUserMenuList")
    public Result getUserMenuList(HttpServletRequest request) {
        String requestId = "REQ-" + System.currentTimeMillis();
        System.out.println("=== SysMenuController.getUserMenuList [" + requestId + "] ===");
        System.out.println("[" + requestId + "] 请求URL: " + request.getRequestURI());
        System.out.println("[" + requestId + "] 请求方法: " + request.getMethod());

        // 打印请求头
        System.out.println("[" + requestId + "] 请求头信息:");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            System.out.println("[" + requestId + "]   " + headerName + ": " + headerValue);
        }

        // 获取当前登录用户id
        Long userId = LoginUserInfoHelper.getUserId();
        System.out.println("[" + requestId + "] 从LoginUserInfoHelper获取的用户ID: " + userId);

        if (userId == null) {
            System.out.println("[" + requestId + "] 用户ID为空，使用默认超级管理员ID");
            // 如果用户ID为空，默认使用超级管理员ID
            userId = 1L;
        }

        System.out.println("[" + requestId + "] 最终使用的用户ID: " + userId);

        List<RouterVo> routerList = sysMenuService.findUserMenuList(userId);
        List<String> permsList = sysMenuService.findUserPermsList(userId);

        System.out.println("[" + requestId + "] Controller返回的路由数量: " + routerList.size());
        System.out.println("[" + requestId + "] Controller返回的权限数量: " + permsList.size());

        // 打印返回的路由详情
        if (routerList != null && routerList.size() > 0) {
            System.out.println("[" + requestId + "] 返回的路由详情:");
            for (int i = 0; i < routerList.size(); i++) {
                RouterVo router = routerList.get(i);
                System.out.println("[" + requestId + "] 路由" + (i+1) + ": " + router.getPath() + " - " + (router.getMeta() != null ? router.getMeta().getTitle() : "无标题"));
            }
        }

        // 确保返回格式与前端期望一致
        Map<String, Object> result = new HashMap<>();
        result.put("routerList", routerList);
        result.put("permsList", permsList);

        System.out.println("[" + requestId + "] === SysMenuController.getUserMenuList 结束 ===");
        return Result.ok(result);
    }

    @Operation(summary = "获取菜单")
    @GetMapping("findNodes")
    public Result<List<SysMenu>> findNodes() {
        List<SysMenu> list = sysMenuService.findNodes();
        return Result.ok(list);
    }

    @Operation(summary = "根据ID获取菜单")
    @GetMapping("getById/{id}")
    public Result<SysMenu> getById(@PathVariable Long id) {
        SysMenu sysMenu = sysMenuService.getById(id);
        return Result.ok(sysMenu);
    }

    @Operation(summary = "新增菜单")
    @PostMapping("save")
    public Result<Boolean> save(@RequestBody SysMenu permission) {
        return Result.ok(sysMenuService.save(permission));
    }

    @Operation(summary = "修改菜单")
    @PutMapping("update")
    public Result<Boolean> update(@RequestBody SysMenu permission) {
        return Result.ok(sysMenuService.updateById(permission));
    }

    @Operation(summary = "删除菜单")
    @DeleteMapping("remove/{id}")
    public Result<Boolean> remove(@PathVariable Long id) {
        // 检查是否有子菜单
        long count = sysMenuService.count(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getParentId, id));
        if (count > 0) {
            return Result.fail("该菜单下有子菜单，请先删除子菜单");
        }
        return Result.ok(sysMenuService.removeById(id));
    }

    @Operation(summary = "根据角色获取菜单")
    @GetMapping("toAssign/{roleId}")
    public Result<List<SysMenu>> toAssign(@PathVariable Long roleId) {
        List<SysMenu> list = sysMenuService.findSysMenuByRoleId(roleId);
        return Result.ok(list);
    }

    @Operation(summary = "给角色分配权限")
    @PostMapping("/doAssign")
    public Result<Boolean> doAssign(@RequestBody AssginMenuVo assginMenuVo) {
        sysMenuService.doAssign(assginMenuVo);
        return Result.ok(true);
    }

//    @Operation(summary = "调试：查看用户权限分配")
//    @GetMapping("/debugUserPermissions/{userId}")
//    public Result<Map<String, Object>> debugUserPermissions(@PathVariable Long userId) {
//        Map<String, Object> result = new HashMap<>();
//
//        // 查询用户信息
//        // 这里需要注入SysUserService，但为了不修改太多代码，我们直接在Service中实现
//        Map<String, Object> debugInfo = sysMenuService.debugUserPermissions(userId);
//
//        return Result.ok(debugInfo);
//    }
}


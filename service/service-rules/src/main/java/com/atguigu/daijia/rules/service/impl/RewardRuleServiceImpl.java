package com.atguigu.daijia.rules.service.impl;

import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.model.entity.rule.RewardRule;
import com.atguigu.daijia.model.form.rules.RewardRuleRequest;
import com.atguigu.daijia.model.form.rules.RewardRuleRequestForm;
import com.atguigu.daijia.model.vo.rules.RewardRuleResponse;
import com.atguigu.daijia.model.vo.rules.RewardRuleResponseVo;
import com.atguigu.daijia.rules.mapper.RewardRuleMapper;
import com.atguigu.daijia.rules.service.RewardRuleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class RewardRuleServiceImpl implements RewardRuleService {
    @Autowired
    private RewardRuleMapper rewardRuleMapper;

    @Autowired
    private KieContainer kieContainer;

    @Override
    public RewardRuleResponseVo calculateOrderRewardFee(RewardRuleRequestForm rewardRuleRequestForm) {
        // 1. 准备输入对象
        RewardRuleRequest rewardRuleRequest = new RewardRuleRequest();
        rewardRuleRequest.setOrderNum(rewardRuleRequestForm.getOrderNum());
        rewardRuleRequest.setStartTime(new DateTime(rewardRuleRequestForm.getStartTime()).toString());

        // 2. 准备输出对象
        RewardRuleResponse rewardRuleResponse = new RewardRuleResponse();

        // 3. 执行规则
        KieSession kieSession = kieContainer.newKieSession();
        try {
            // 设置全局变量
            kieSession.setGlobal("rewardRuleResponse", rewardRuleResponse);

            // 插入事实
            kieSession.insert(rewardRuleRequest);

            // 触发规则
            kieSession.fireAllRules();
        } catch (Exception e) {
            log.error("执行奖励规则出错", e);
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        } finally {
            // 释放资源
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        // 4. 获取或创建规则记录
        RewardRule rewardRule = getOrCreateRewardRule();

        // 5. 转换结果
        RewardRuleResponseVo responseVo = new RewardRuleResponseVo();
        responseVo.setRewardAmount(rewardRuleResponse.getRewardAmount());
        responseVo.setRewardRuleId(rewardRule.getId()); // 设置规则ID
        return responseVo;
    }

    /**
     * 获取或创建奖励规则
     */
    private RewardRule getOrCreateRewardRule() {
        // 查询有效的奖励规则
        LambdaQueryWrapper<RewardRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RewardRule::getStatus, 1);
        queryWrapper.orderByDesc(RewardRule::getUpdateTime);
        queryWrapper.last("limit 1");

        RewardRule rewardRule = rewardRuleMapper.selectOne(queryWrapper);

        // 如果没有有效规则，创建一个默认规则
        if (rewardRule == null) {
            rewardRule = new RewardRule();
            rewardRule.setName("默认奖励规则");
            rewardRule.setRule("rules/RewardRule.drl");
            rewardRule.setStatus(1);
            rewardRule.setCreateTime(new Date());
            rewardRule.setUpdateTime(new Date());
            rewardRuleMapper.insert(rewardRule);
        }
        return rewardRule;
    }
}

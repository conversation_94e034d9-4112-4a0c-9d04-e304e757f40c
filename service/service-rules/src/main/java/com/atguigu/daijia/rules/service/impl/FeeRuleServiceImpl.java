package com.atguigu.daijia.rules.service.impl;

import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.model.entity.rule.FeeRule;
import com.atguigu.daijia.model.form.rules.FeeRuleRequest;
import com.atguigu.daijia.model.form.rules.FeeRuleRequestForm;
import com.atguigu.daijia.model.vo.rules.FeeRuleResponse;
import com.atguigu.daijia.model.vo.rules.FeeRuleResponseVo;
import com.atguigu.daijia.rules.mapper.FeeRuleMapper;
import com.atguigu.daijia.rules.service.FeeRuleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class FeeRuleServiceImpl implements FeeRuleService {


    @Autowired
    private FeeRuleMapper feeRuleMapper;

    @Autowired
    private KieContainer kieContainer;

    @Override
    public FeeRuleResponseVo calculateOrderFee(FeeRuleRequestForm calculateOrderFeeForm) {
        // 1. 准备输入对象
        FeeRuleRequest feeRuleRequest = new FeeRuleRequest();
        feeRuleRequest.setDistance(calculateOrderFeeForm.getDistance());
        feeRuleRequest.setStartTime(new DateTime(calculateOrderFeeForm.getStartTime()).toString());
        feeRuleRequest.setWaitMinute(calculateOrderFeeForm.getWaitMinute());

        // 2. 准备输出对象
        FeeRuleResponse feeRuleResponse = new FeeRuleResponse();

        // 3. 执行规则
        KieSession kieSession = kieContainer.newKieSession();
        try {
            // 设置全局变量
            kieSession.setGlobal("feeRuleResponse", feeRuleResponse);

            // 插入事实
            kieSession.insert(feeRuleRequest);

            // 触发规则
            kieSession.fireAllRules();
        } catch (Exception e) {
            log.error("执行费用规则出错", e);
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        } finally {
            // 释放资源
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        // 4. 获取或创建规则记录
        FeeRule feeRule = getOrCreateFeeRule();

        // 5. 转换结果
        FeeRuleResponseVo responseVo = new FeeRuleResponseVo();
        BeanUtils.copyProperties(feeRuleResponse, responseVo);
        responseVo.setFeeRuleId(feeRule.getId()); // 设置规则ID
        return responseVo;
    }

    /**
     * 获取或创建费用规则
     */
    private FeeRule getOrCreateFeeRule() {
        // 查询有效的费用规则
        LambdaQueryWrapper<FeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FeeRule::getStatus, 1);
        queryWrapper.orderByDesc(FeeRule::getUpdateTime);
        queryWrapper.last("limit 1");

        FeeRule feeRule = feeRuleMapper.selectOne(queryWrapper);

        // 如果没有有效规则，创建一个默认规则
        if (feeRule == null) {
            feeRule = new FeeRule();
            feeRule.setName("默认费用规则");
            feeRule.setRule("rules/FeeRule.drl");
            feeRule.setStatus(1);
            feeRule.setCreateTime(new Date());
            feeRule.setUpdateTime(new Date());
            feeRuleMapper.insert(feeRule);
        }

        return feeRule;
    }
}

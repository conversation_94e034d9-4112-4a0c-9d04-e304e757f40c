package com.atguigu.daijia.rules.service.impl;

import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.model.entity.rule.ProfitsharingRule;
import com.atguigu.daijia.model.form.rules.ProfitsharingRuleRequest;
import com.atguigu.daijia.model.form.rules.ProfitsharingRuleRequestForm;
import com.atguigu.daijia.model.vo.rules.ProfitsharingRuleResponse;
import com.atguigu.daijia.model.vo.rules.ProfitsharingRuleResponseVo;
import com.atguigu.daijia.rules.mapper.ProfitsharingRuleMapper;
import com.atguigu.daijia.rules.service.ProfitsharingRuleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class ProfitsharingRuleServiceImpl implements ProfitsharingRuleService {

    @Autowired
    private ProfitsharingRuleMapper profitsharingRuleMapper;

    @Autowired
    private KieContainer kieContainer;

    @Override
    public ProfitsharingRuleResponseVo calculateOrderProfitsharingFee(ProfitsharingRuleRequestForm profitsharingRuleRequestForm) {
        // 1. 封装传入对象
        ProfitsharingRuleRequest profitsharingRuleRequest = new ProfitsharingRuleRequest();
        profitsharingRuleRequest.setOrderAmount(profitsharingRuleRequestForm.getOrderAmount());
        profitsharingRuleRequest.setOrderNum(profitsharingRuleRequestForm.getOrderNum());

        // 2. 封装返回对象
        ProfitsharingRuleResponse profitsharingRuleResponse = new ProfitsharingRuleResponse();

        // 3. 执行规则
        KieSession kieSession = kieContainer.newKieSession();
        try {
            // 设置全局变量
            kieSession.setGlobal("profitsharingRuleResponse", profitsharingRuleResponse);

            // 插入事实
            kieSession.insert(profitsharingRuleRequest);

            // 触发规则
            kieSession.fireAllRules();
        } catch (Exception e) {
            log.error("执行分账规则出错", e);
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        } finally {
            // 释放资源
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        // 4. 获取或创建规则记录
        ProfitsharingRule profitsharingRule = getOrCreateProfitsharingRule();

        // 5. 封装返回对象
        ProfitsharingRuleResponseVo profitsharingRuleResponseVo = new ProfitsharingRuleResponseVo();
        BeanUtils.copyProperties(profitsharingRuleResponse, profitsharingRuleResponseVo);
        profitsharingRuleResponseVo.setProfitsharingRuleId(profitsharingRule.getId()); // 设置规则ID
        return profitsharingRuleResponseVo;
    }

    /**
     * 获取或创建分账规则
     */
    private ProfitsharingRule getOrCreateProfitsharingRule() {
        // 查询有效的分账规则
        LambdaQueryWrapper<ProfitsharingRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProfitsharingRule::getStatus, 1);
        queryWrapper.orderByDesc(ProfitsharingRule::getUpdateTime);
        queryWrapper.last("limit 1");

        ProfitsharingRule profitsharingRule = profitsharingRuleMapper.selectOne(queryWrapper);

        // 如果没有有效规则，创建一个默认规则
        if (profitsharingRule == null) {
            profitsharingRule = new ProfitsharingRule();
            profitsharingRule.setName("默认分账规则");
            profitsharingRule.setRule("rules/ProfitsharingRule.drl");
            profitsharingRule.setStatus(1);
            profitsharingRule.setCreateTime(new Date());
            profitsharingRule.setUpdateTime(new Date());
            profitsharingRuleMapper.insert(profitsharingRule);
        }

        return profitsharingRule;
    }
}

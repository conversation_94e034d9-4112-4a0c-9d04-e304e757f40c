package com.atguigu.daijia.rules.config;

import lombok.extern.slf4j.Slf4j;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.builder.KieScanner;
import org.kie.api.runtime.KieContainer;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class DroolsConfig {
    // 制定规则文件的路径
    // 支持多规则文件
    private static final String[] RULE_FILES = {
            "rules/FeeRule.drl",
            "rules/RewardRule.drl",
            "rules/ProfitsharingRule.drl"
    };

    @Bean
    public KieContainer kieContainer() {
        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

        // 加载所有规则文件
        for (String ruleFile : RULE_FILES) {
            kieFileSystem.write(ResourceFactory
                    .newClassPathResource(ruleFile));
        }

        // 编译规则
        KieBuilder kb = kieServices.newKieBuilder(kieFileSystem);
        kb.buildAll();

        // 添加KieScanner支持热更新
        KieModule kieModule = kb.getKieModule();
        KieContainer kieContainer = kieServices.newKieContainer(kieModule.getReleaseId());

        KieScanner kieScanner = kieServices.newKieScanner(kieContainer);
        kieScanner.start(10_000); // 每10秒检查更新

        return kieContainer;
    }
}

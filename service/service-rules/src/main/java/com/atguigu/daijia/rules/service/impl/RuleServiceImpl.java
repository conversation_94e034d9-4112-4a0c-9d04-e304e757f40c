//package com.atguigu.daijia.rules.service.impl;
//
//import com.atguigu.daijia.common.execption.GuiguException;
//import com.atguigu.daijia.common.result.ResultCodeEnum;
//import com.atguigu.daijia.model.form.rules.*;
//import com.atguigu.daijia.model.vo.rules.*;
//import com.atguigu.daijia.rules.service.FeeRuleService;
//import com.atguigu.daijia.rules.service.ProfitsharingRuleService;
//import com.atguigu.daijia.rules.service.RewardRuleService;
//import org.joda.time.DateTime;
//import org.kie.api.runtime.KieContainer;
//import org.kie.api.runtime.KieSession;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Service;
//
//
//@Service
//@Primary
//public class RuleServiceImpl implements FeeRuleService, RewardRuleService, ProfitsharingRuleService {
//
//    @Autowired
//    private KieContainer kieContainer;
//
//    // 计算订单费用
//    @Override
//    public FeeRuleResponseVo calculateOrderFee(FeeRuleRequestForm form) {
//        // 1. 准备输入对象
//        FeeRuleRequest feeRuleRequest = new FeeRuleRequest();
//        feeRuleRequest.setDistance(form.getDistance());
//        feeRuleRequest.setStartTime(new DateTime(form.getStartTime()).toString());
//        feeRuleRequest.setWaitMinute(form.getWaitMinute());
//
//        // 2. 准备输出对象
//        FeeRuleResponse feeRuleResponse = new FeeRuleResponse();
//
//        // 3. 执行规则
//        executeRules(feeRuleRequest, feeRuleResponse);
//
//        // 4. 转换结果
//        FeeRuleResponseVo responseVo = new FeeRuleResponseVo();
//        BeanUtils.copyProperties(feeRuleResponse, responseVo);
//        return responseVo;
//    }
//
//    // 计算订单奖励费用
//    @Override
//    public RewardRuleResponseVo calculateOrderRewardFee(RewardRuleRequestForm rewardRuleRequestForm) {
//        // 1. 准备输入对象
//        RewardRuleRequest rewardRuleRequest = new RewardRuleRequest();
//        rewardRuleRequest.setOrderNum(rewardRuleRequestForm.getOrderNum());
//        rewardRuleRequest.setStartTime(new DateTime(rewardRuleRequestForm.getStartTime()).toString());
//
//        // 2. 准备输出对象
//        RewardRuleResponse rewardRuleResponse = new RewardRuleResponse();
//
//        // 3. 执行规则
//        executeRules(rewardRuleRequest, rewardRuleResponse);
//
//        // 4. 转换结果
//        RewardRuleResponseVo responseVo = new RewardRuleResponseVo();
//        responseVo.setRewardAmount(rewardRuleResponse.getRewardAmount());
//        return responseVo;
//    }
//
//    // 计算订单分账费用
//    @Override
//    public ProfitsharingRuleResponseVo calculateOrderProfitsharingFee(ProfitsharingRuleRequestForm profitsharingRuleRequestForm) {
//        //封装传入对象
//        ProfitsharingRuleRequest profitsharingRuleRequest = new ProfitsharingRuleRequest();
//        profitsharingRuleRequest.setOrderAmount(profitsharingRuleRequestForm.getOrderAmount());
//        profitsharingRuleRequest.setOrderNum(profitsharingRuleRequestForm.getOrderNum());
//        //封装返回对象
//        ProfitsharingRuleResponse profitsharingRuleResponse = new ProfitsharingRuleResponse();
//        // 3. 执行规则
//        executeRules(profitsharingRuleRequest, profitsharingRuleResponse);
//        //封装返回对象
//        ProfitsharingRuleResponseVo profitsharingRuleResponseVo = new ProfitsharingRuleResponseVo();
//        BeanUtils.copyProperties(profitsharingRuleResponse, profitsharingRuleResponseVo);
//        return profitsharingRuleResponseVo;
//    }
//
//    /**
//     * 通用规则执行方法
//     * @param input 规则输入对象
//     * @param output 规则输出对象
//     */
//    private void executeRules(Object input, Object output) {
//        KieSession session = null;
//        try {
//            // 1. 创建新会话
//            session = kieContainer.newKieSession();
//
//            // 2. 设置全局变量（规则输出对象）
//            // 根据输出类型设置正确的全局变量名
//            if (output instanceof FeeRuleResponse) {
//                session.setGlobal("feeRuleResponse", output);
//            } else if (output instanceof RewardRuleResponse) {
//                session.setGlobal("rewardRuleResponse", output);
//            }else if (output instanceof ProfitsharingRuleResponse){
//                session.setGlobal("profitsharingRuleResponse", output);
//            }
//
//            // 3. 插入事实对象（规则输入对象）
//            session.insert(input);
//
//            // 4. 执行规则
//            session.fireAllRules();
//        } catch (Exception e) {
//            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
//        } finally {
//            // 5. 确保资源释放
//            if (session != null) {
//                session.dispose();
//            }
//        }
//    }
//}

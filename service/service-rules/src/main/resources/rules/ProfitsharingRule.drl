//package对应的不一定是真正的目录，可以任意写com.abc，同一个包下的drl文件可以相互访问
package  com.atguigu.daijia

import com.atguigu.daijia.model.form.rules.ProfitsharingRuleRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

global com.atguigu.daijia.model.vo.rules.ProfitsharingRuleResponse profitsharingRuleResponse;
//支付微信平台费率：0.6%
//global BigDecimal paymentRate = new BigDecimal(0.006);
/**
支付微信平台费用
    平台费率：0.6%
*/
rule "支付微信平台费用 平台费率：0.6%"
    salience 10          //指定优先级，数值越大优先级越高，不指定的情况下由上到下执行
    no-loop true         //防止陷入死循环
    when
        /*规则条件，到工作内存中查找FeeRuleRequest对象
        里面出来的结果只能是ture或者false
        $rule是绑定变量名，可以任意命名，官方推荐$符号，定义了绑定变量名，可以在then部分操作fact对象*/
        $rule:ProfitsharingRuleRequest()
    then
        profitsharingRuleResponse.setOrderAmount($rule.getOrderAmount());
        profitsharingRuleResponse.setPaymentRate(new BigDecimal("0.006"));
        BigDecimal paymentFee = profitsharingRuleResponse.getOrderAmount().multiply(profitsharingRuleResponse.getPaymentRate()).setScale(2, RoundingMode.HALF_UP);
        profitsharingRuleResponse.setPaymentFee(paymentFee);

        // 设置默认值，确保即使其他规则不匹配，这些字段也有值
        profitsharingRuleResponse.setPlatformIncome(BigDecimal.ZERO);
        profitsharingRuleResponse.setDriverIncome(BigDecimal.ZERO);
        profitsharingRuleResponse.setDriverTaxRate(BigDecimal.ZERO);
        profitsharingRuleResponse.setDriverTaxFee(BigDecimal.ZERO);

        System.out.println("支付微信平台费用：" + profitsharingRuleResponse.getPaymentFee() + "元");
end

/**
订单金额小于等于100
    当天完成订单小于等于10单 平台抽成 20%
    当天完成订单大于10单 平台抽成 18%
*/
rule "订单金额小于等于100 当天完成订单小于等于10单"
    salience 10          //指定优先级，数值越大优先级越高，不指定的情况下由上到下执行
    no-loop true         //防止陷入死循环
    when
        /*规则条件，到工作内存中查找FeeRuleRequest对象
        里面出来的结果只能是ture或者false
        $rule是绑定变量名，可以任意命名，官方推荐$符号，定义了绑定变量名，可以在then部分操作fact对象*/
        $rule:ProfitsharingRuleRequest(orderAmount.doubleValue() <= 100.0 && orderNum <= 10)
    then
        BigDecimal totalAmount = profitsharingRuleResponse.getOrderAmount().subtract(profitsharingRuleResponse.getPaymentFee());
        BigDecimal platformIncome = totalAmount.multiply(new BigDecimal("0.2")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverTotalIncome = totalAmount.subtract(platformIncome);
        //代驾司机个税，税率：10%
        BigDecimal driverTaxFee = driverTotalIncome.multiply(new BigDecimal("0.1")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverIncome = driverTotalIncome.subtract(driverTaxFee);
        profitsharingRuleResponse.setPlatformIncome(platformIncome);
        profitsharingRuleResponse.setDriverIncome(driverIncome);
        profitsharingRuleResponse.setDriverTaxRate(new BigDecimal("0.1"));
        profitsharingRuleResponse.setDriverTaxFee(driverTaxFee);
        System.out.println("平台分账收入：" + platformIncome + "元" + "，司机分账收入：" + driverIncome + "元" + "，司机个税：" + driverTaxFee + "元");
end
rule "订单金额小于等于100 天完成订单大于10单"
    salience 10          //指定优先级，数值越大优先级越高，不指定的情况下由上到下执行
    no-loop true         //防止陷入死循环
    when
        /*规则条件，到工作内存中查找FeeRuleRequest对象
        里面出来的结果只能是ture或者false
        $rule是绑定变量名，可以任意命名，官方推荐$符号，定义了绑定变量名，可以在then部分操作fact对象*/
        $rule:ProfitsharingRuleRequest(orderAmount.doubleValue() <= 100.0 && orderNum > 10)
    then
        BigDecimal totalAmount = profitsharingRuleResponse.getOrderAmount().subtract(profitsharingRuleResponse.getPaymentFee());
        BigDecimal platformIncome = totalAmount.multiply(new BigDecimal("0.18")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverTotalIncome = totalAmount.subtract(platformIncome);
        //代驾司机个税，税率：10%
        BigDecimal driverTaxFee = driverTotalIncome.multiply(new BigDecimal("0.1")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverIncome = driverTotalIncome.subtract(driverTaxFee);
        profitsharingRuleResponse.setPlatformIncome(platformIncome);
        profitsharingRuleResponse.setDriverIncome(driverIncome);
        profitsharingRuleResponse.setDriverTaxRate(new BigDecimal("0.1"));
        profitsharingRuleResponse.setDriverTaxFee(driverTaxFee);
        System.out.println("平台分账收入：" + platformIncome + "元" + "，司机分账收入：" + driverIncome + "元" + "，司机个税：" + driverTaxFee + "元");
end

/**
订单金额大于100
    当天完成订单小于等于10单 平台抽成 18%
    当天完成订单大于10单 平台抽成 16%
*/
rule "订单金额大于100 当天完成订单小于等于10单"
    salience 10          //指定优先级，数值越大优先级越高，不指定的情况下由上到下执行
    no-loop true         //防止陷入死循环
    when
        /*规则条件，到工作内存中查找FeeRuleRequest对象
        里面出来的结果只能是ture或者false
        $rule是绑定变量名，可以任意命名，官方推荐$符号，定义了绑定变量名，可以在then部分操作fact对象*/
        $rule:ProfitsharingRuleRequest(orderAmount.doubleValue() > 100.0 && orderNum <= 10)
    then
        BigDecimal totalAmount = profitsharingRuleResponse.getOrderAmount().subtract(profitsharingRuleResponse.getPaymentFee());
        BigDecimal platformIncome = totalAmount.multiply(new BigDecimal("0.18")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverTotalIncome = totalAmount.subtract(platformIncome);
        //代驾司机个税，税率：10%
        BigDecimal driverTaxFee = driverTotalIncome.multiply(new BigDecimal("0.1")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverIncome = driverTotalIncome.subtract(driverTaxFee);
        profitsharingRuleResponse.setPlatformIncome(platformIncome);
        profitsharingRuleResponse.setDriverIncome(driverIncome);
        profitsharingRuleResponse.setDriverTaxRate(new BigDecimal("0.1"));
        profitsharingRuleResponse.setDriverTaxFee(driverTaxFee);
        System.out.println("平台分账收入：" + platformIncome + "元" + "，司机分账收入：" + driverIncome + "元" + "，司机个税：" + driverTaxFee + "元");
end
rule "订单金额大于100 天完成订单大于10单"
    salience 10          //指定优先级，数值越大优先级越高，不指定的情况下由上到下执行
    no-loop true         //防止陷入死循环
    when
        /*规则条件，到工作内存中查找FeeRuleRequest对象
        里面出来的结果只能是ture或者false
        $rule是绑定变量名，可以任意命名，官方推荐$符号，定义了绑定变量名，可以在then部分操作fact对象*/
        $rule:ProfitsharingRuleRequest(orderAmount.doubleValue() > 100.0 && orderNum > 10)
    then
        BigDecimal totalAmount = profitsharingRuleResponse.getOrderAmount().subtract(profitsharingRuleResponse.getPaymentFee());
        BigDecimal platformIncome = totalAmount.multiply(new BigDecimal("0.18")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverTotalIncome = totalAmount.subtract(platformIncome);
        //代驾司机个税，税率：10%
        BigDecimal driverTaxFee = driverTotalIncome.multiply(new BigDecimal("0.1")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal driverIncome = driverTotalIncome.subtract(driverTaxFee);
        profitsharingRuleResponse.setPlatformIncome(platformIncome);
        profitsharingRuleResponse.setDriverIncome(driverIncome);
        profitsharingRuleResponse.setDriverTaxRate(new BigDecimal("0.1"));
        profitsharingRuleResponse.setDriverTaxFee(driverTaxFee);
        System.out.println("平台分账收入：" + platformIncome + "元" + "，司机分账收入：" + driverIncome + "元" + "，司机个税：" + driverTaxFee + "元");
end

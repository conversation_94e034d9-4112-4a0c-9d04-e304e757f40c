package com.atguigu.daijia.map.document;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.GeoPointField;
import org.springframework.data.elasticsearch.core.geo.GeoPoint;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Document(indexName = "driver_location")
public class DriverLocationDocument {

    @Id
    private String id;

    @Field(type = FieldType.Long)
    private Long driverId;

    @GeoPointField
    private GeoPoint location;

    @Field(type = FieldType.Date)
    private Date updateTime;

    @Field(type = FieldType.Integer)
    private Integer status; // 1-在线 0-离线

    // 新增：司机个性化设置
    @Field(type = FieldType.Double)
    private BigDecimal acceptDistance; // 接单距离

    @Field(type = FieldType.Double)
    private BigDecimal orderDistance;  // 订单距离
}

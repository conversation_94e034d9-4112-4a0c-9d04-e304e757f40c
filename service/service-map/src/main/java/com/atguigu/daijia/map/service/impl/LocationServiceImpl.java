package com.atguigu.daijia.map.service.impl;

import com.atguigu.daijia.common.constant.RedisConstant;
import com.atguigu.daijia.common.constant.SystemConstant;
import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.common.util.LocationUtil;
import com.atguigu.daijia.driver.client.DriverInfoFeignClient;
import com.atguigu.daijia.map.repository.OrderServiceLocationRepository;
import com.atguigu.daijia.map.service.LocationService;
import com.atguigu.daijia.model.entity.driver.DriverSet;
import com.atguigu.daijia.model.entity.map.OrderServiceLocation;
import com.atguigu.daijia.model.form.map.OrderServiceLocationForm;
import com.atguigu.daijia.model.form.map.SearchNearByDriverForm;
import com.atguigu.daijia.model.form.map.UpdateDriverLocationForm;
import com.atguigu.daijia.model.form.map.UpdateOrderLocationForm;
import com.atguigu.daijia.model.vo.map.NearByDriverVo;
import com.atguigu.daijia.model.vo.map.OrderLocationVo;
import com.atguigu.daijia.model.vo.map.OrderServiceLastLocationVo;
import com.atguigu.daijia.order.client.OrderInfoFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.connection.RedisCommands;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class LocationServiceImpl implements LocationService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private DriverInfoFeignClient driverInfoFeignClient;

    @Autowired
    private OrderServiceLocationRepository orderServiceLocationRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private OrderInfoFeignClient orderInfoFeignClient;

    @Autowired
    private LocationESServiceImpl locationESService;
    // 更新司机经纬度位置
    @Override
    public Boolean updateDriverLocation(UpdateDriverLocationForm updateDriverLocationForm) {
        // Redis存储（保持原有逻辑）
        Point point=new Point(updateDriverLocationForm.getLongitude().doubleValue(),
                                updateDriverLocationForm.getLatitude().doubleValue());
        redisTemplate.opsForGeo().add(RedisConstant.DRIVER_GEO_LOCATION,
                point, updateDriverLocationForm.getDriverId().toString());

        // 同时存储到ES（新增）
        try {
            locationESService.updateDriverLocationES(updateDriverLocationForm);
            log.info("司机位置同步到ES成功，司机ID：{}", updateDriverLocationForm.getDriverId());
        } catch (Exception e) {
            log.error("司机位置同步到ES失败，司机ID：{}", updateDriverLocationForm.getDriverId(), e);
            // ES失败不影响Redis操作，继续返回成功
        }

        return true;
    }

    // 删除司机经纬度位置
    @Override
    public Boolean removeDriverLocation(Long driverId) {
        // Redis删除（保持原有逻辑）
        redisTemplate.opsForGeo().remove(RedisConstant.DRIVER_GEO_LOCATION, driverId.toString());

        // 同时从ES删除（新增）
        try {
            locationESService.removeDriverLocationES(driverId);
            log.info("司机位置从ES删除成功，司机ID：{}", driverId);
        } catch (Exception e) {
            log.error("司机位置从ES删除失败，司机ID：{}", driverId, e);
            // ES失败不影响Redis操作，继续返回成功
        }

        return true;
    }

    // 搜索附近满足条件的司机
    @Override
    public List<NearByDriverVo> searchNearByDriver(SearchNearByDriverForm searchNearByDriverForm) {
        //搜索经纬度位置5公里以内的司机
        //1 操作redis里面geo
        // 创建经纬度位置
        Point point=new Point(searchNearByDriverForm.getLongitude().doubleValue(),
                searchNearByDriverForm.getLatitude().doubleValue());
        // 定义搜索半径，5公里
        Distance distance =
                new Distance(SystemConstant.NEARBY_DRIVER_RADIUS, RedisGeoCommands.DistanceUnit.KILOMETERS);
        // 定义以point点为中心，distance为距离这么一个范围
        Circle circle=new Circle(point, distance);

        //定义GEO参数,
        RedisGeoCommands.GeoRadiusCommandArgs args=
                RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                        .includeDistance()// 包含距离 在结果中包含每个司机距离中心点的距离
                        .includeCoordinates()// 包含坐标 在结果中包含每个司机的经纬度坐标
                        .sortAscending(); // 升序 按距离从近到远排序

        GeoResults<RedisGeoCommands.GeoLocation<String>> result =
                redisTemplate.opsForGeo().radius(RedisConstant.DRIVER_GEO_LOCATION, circle, args);

        //2 查询redis最终返回list集台
        List<GeoResult<RedisGeoCommands.GeoLocation<String>>> content =result.getContent();

        // 使用Stream API处理数据
        List<NearByDriverVo> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(content)) {
            list = content.stream()
                    .map(item -> {
                        // 获取司机id
                        Long driverId = Long.valueOf(item.getContent().getName());
                        // 远程调用，根据司机id个性化设置信息
                        Result<DriverSet> driverSetResult = driverInfoFeignClient.getDriverSet(driverId);
                        DriverSet driverSet = driverSetResult.getData();

                        // 判断订单里程
                        BigDecimal orderDistance = driverSet.getOrderDistance();
                        boolean orderDistanceValid = orderDistance.doubleValue() == 0 ||
                                orderDistance.subtract(searchNearByDriverForm.getMileageDistance()).doubleValue() >= 0;

                        // 判断接单里程 accept_distance 计算司机与乘客之间的当前距离，并保留两位小数（向上取整）
                        BigDecimal currentDistance=new BigDecimal(item.getDistance().getValue()).setScale(2, RoundingMode.UP);
                        BigDecimal acceptDistance = driverSet.getAcceptDistance();
                            boolean acceptDistanceValid = acceptDistance.doubleValue() == 0 ||
                                acceptDistance.subtract(currentDistance).doubleValue() >= 0;

                        // 如果两个条件都满足，创建并返回NearByDriverVo对象，否则返回null
                        if (orderDistanceValid && acceptDistanceValid) {
                            NearByDriverVo nearByDriverVo = new NearByDriverVo();
                            nearByDriverVo.setDriverId(driverId);
                            nearByDriverVo.setDistance(currentDistance);
                            return nearByDriverVo;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull) // 过滤掉null值
                    .collect(Collectors.toList()); // 收集结果到列表
        }
        return list;
    }


    // 司机赶往代驾起始点:更新订单地址到缓存
    @Override
    public Boolean updateOrderLocationToCache(UpdateOrderLocationForm updateOrderLocationForm) {
        OrderLocationVo orderLocationVo = new OrderLocationVo();
        orderLocationVo.setLongitude(updateOrderLocationForm.getLongitude());
        orderLocationVo.setLatitude(updateOrderLocationForm.getLatitude());
        String key=RedisConstant.UPDATE_ORDER_LOCATION + updateOrderLocationForm.getOrderId();
        redisTemplate.opsForValue().set(key,orderLocationVo);
        return true;
    }

    // 司机赶往代驾起始点:获取订单经纬度位置
    @Override
    public OrderLocationVo getCacheOrderLocation(Long orderId) {
        String key=RedisConstant.UPDATE_ORDER_LOCATION + orderId;
        OrderLocationVo orderLocationVo =
                (OrderLocationVo)redisTemplate.opsForValue().get(key);
        return orderLocationVo;
    }


    // 开始代驾服务：保存代驾服务订单位置
    @Override
    public Boolean saveOrderServiceLocation(List<OrderServiceLocationForm> orderLocationServiceFormList) {
        List<OrderServiceLocation> list = orderLocationServiceFormList
                .stream().map(orderServiceLocationForm -> {
            OrderServiceLocation orderServiceLocation = new OrderServiceLocation();
            BeanUtils.copyProperties(orderServiceLocationForm,orderServiceLocation);
            orderServiceLocation.setId(ObjectId.get().toString());
            orderServiceLocation.setCreateTime(new Date());
            return orderServiceLocation;
        }).collect(Collectors.toList());
        orderServiceLocationRepository.saveAll(list);
        return true;
    }

    // 获取代驾服务订单最后位置
    @Override
    public OrderServiceLastLocationVo getOrderServiceLastLocation(Long orderId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("orderId").is(orderId));
        query.with(Sort.by(Sort.Order.desc("createTime")));
        query.limit(1);
        OrderServiceLocation orderServiceLocation =
                mongoTemplate.findOne(query, OrderServiceLocation.class);
        OrderServiceLastLocationVo orderServiceLastLocationVo = new OrderServiceLastLocationVo();
        BeanUtils.copyProperties(orderServiceLocation,orderServiceLastLocationVo);
        return orderServiceLastLocationVo;
    }

    // 代驾服务：计算订单实际里程
    @Override
    public BigDecimal calculateOrderRealDistance(Long orderId) {
        //根据订单id获取代驾订单位置信息，根据创建时间排序（升序）
        // findByOrderIdOrderByCreateTimeAsc
        List<OrderServiceLocation> orderServiceLocationList =
                orderServiceLocationRepository.findByOrderIdOrderByCreateTimeAsc(orderId);
        // 第一步查询返回订单位置信息list集合
        // 把list集合遍历，得到每个位置信息，计算两个位置距离
        // 把计算所有距离相加操作
        double realDistance = 0;
        if(!CollectionUtils.isEmpty(orderServiceLocationList)) {
            for (int i = 0, size=orderServiceLocationList.size()-1; i < size; i++) {
                OrderServiceLocation location1 = orderServiceLocationList.get(i);
                OrderServiceLocation location2 = orderServiceLocationList.get(i+1);

                double distance = LocationUtil.getDistance
                        (location1.getLatitude().doubleValue(), location1.getLongitude().doubleValue(),
                                location2.getLatitude().doubleValue(), location2.getLongitude().doubleValue());
                realDistance += distance;
            }
        }
        //测试过程中，没有真正代驾，实际代驾GPS位置没有变化，模拟：实际代驾里程 = 预期里程 + 5
        if(realDistance == 0) {
            return orderInfoFeignClient.getOrderInfo(orderId).getData().getExpectDistance().add(new BigDecimal("5"));
        }

        // 返回最终计算实际距离
        return new BigDecimal(realDistance);
    }
}

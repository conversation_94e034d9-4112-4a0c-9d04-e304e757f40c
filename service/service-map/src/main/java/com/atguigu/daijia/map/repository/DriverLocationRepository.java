package com.atguigu.daijia.map.repository;

import com.atguigu.daijia.map.document.DriverLocationDocument;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

/**
 * @date 2025/8/11
 * @description 描述
 */
public interface DriverLocationRepository extends ElasticsearchRepository<DriverLocationDocument, String> {

    void deleteByDriverId(Long driverId);

    DriverLocationDocument findByDriverId(Long driverId);
}

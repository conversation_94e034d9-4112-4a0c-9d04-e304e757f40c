package com.atguigu.daijia.map.service.impl;

import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.driver.client.DriverInfoFeignClient;
import com.atguigu.daijia.map.document.DriverLocationDocument;
import com.atguigu.daijia.map.repository.DriverLocationRepository;
import com.atguigu.daijia.model.entity.driver.DriverSet;
import com.atguigu.daijia.model.form.map.SearchNearByDriverForm;
import com.atguigu.daijia.model.form.map.UpdateDriverLocationForm;
import com.atguigu.daijia.model.vo.map.NearByDriverVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.geo.GeoPoint;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LocationESServiceImpl {

    @Autowired
    private DriverLocationRepository driverLocationRepository;

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @Autowired
    private DriverInfoFeignClient driverInfoFeignClient;

    // ES版本：更新司机位置
    public Boolean updateDriverLocationES(UpdateDriverLocationForm form) {
        try {
            // 远程调用获取司机个性化设置
            Result<DriverSet> driverSetResult = driverInfoFeignClient.getDriverSet(form.getDriverId());
            DriverSet driverSet = driverSetResult.getData();

            DriverLocationDocument existingDoc = driverLocationRepository.findByDriverId(form.getDriverId());

            DriverLocationDocument document;
            if (existingDoc != null) {
                document = existingDoc;
            } else {
                document = new DriverLocationDocument();
                document.setDriverId(form.getDriverId());
            }

            // 更新位置信息
            document.setLocation(new GeoPoint(form.getLatitude().doubleValue(), form.getLongitude().doubleValue()));
            document.setUpdateTime(new Date());
            document.setStatus(1);

            // 同步司机个性化设置到ES
            if (driverSet != null) {
                document.setAcceptDistance(driverSet.getAcceptDistance());
                document.setOrderDistance(driverSet.getOrderDistance());
            }

            driverLocationRepository.save(document);
            log.info("ES更新司机位置和设置成功，司机ID：{}", form.getDriverId());
            return true;
        } catch (Exception e) {
            log.error("ES更新司机位置失败，司机ID：{}", form.getDriverId(), e);
            return false;
        }
    }

    // ES版本：删除司机位置
    public Boolean removeDriverLocationES(Long driverId) {
        try {
            driverLocationRepository.deleteByDriverId(driverId);
            log.info("ES删除司机位置成功，司机ID：{}", driverId);
            return true;
        } catch (Exception e) {
            log.error("ES删除司机位置失败，司机ID：{}", driverId, e);
            return false;
        }
    }

    // ES版本：搜索附近司机（使用脚本查询，ES内部完成所有条件过滤）
    public List<NearByDriverVo> searchNearByDriverES(SearchNearByDriverForm form) {
        try {
            // 使用ES脚本查询，在ES内部进行所有条件判断
            String scriptSource = """
                // 计算司机与乘客的距离（公里）
                double distance = doc['location'].arcDistance(params.customerLat, params.customerLon) / 1000.0;

                // 判断接单距离条件
                boolean acceptValid = true;
                if (!doc['acceptDistance'].empty && doc['acceptDistance'].value > 0) {
                    acceptValid = doc['acceptDistance'].value >= distance;
                }

                // 判断订单距离条件
                boolean orderValid = true;
                if (!doc['orderDistance'].empty && doc['orderDistance'].value > 0) {
                    orderValid = doc['orderDistance'].value >= params.mileageDistance;
                }

                // 返回是否满足所有条件
                return acceptValid && orderValid;
                """;

            // 设置脚本参数
            Map<String, Object> scriptParams = new HashMap<>();
            scriptParams.put("customerLat", form.getLatitude().doubleValue());
            scriptParams.put("customerLon", form.getLongitude().doubleValue());
            scriptParams.put("mileageDistance", form.getMileageDistance().doubleValue());

            // 创建脚本对象
            Script script = new Script(ScriptType.INLINE, "painless", scriptSource, scriptParams);

            // 构建查询：地理位置范围 + 在线状态 + 脚本过滤
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("status", 1))
                    .filter(QueryBuilders.geoDistanceQuery("location")
                            .point(form.getLatitude().doubleValue(), form.getLongitude().doubleValue())
                            .distance("5km"))
                    .filter(QueryBuilders.scriptQuery(script));

            // 构建原生搜索查询
            NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withQuery(boolQuery)
                    .withMaxResults(50) // 限制返回数量
                    .build();

            SearchHits<DriverLocationDocument> searchHits = elasticsearchOperations.search(searchQuery, DriverLocationDocument.class);

            // ES脚本已完成所有条件过滤，直接处理结果
            List<NearByDriverVo> result = searchHits.stream()
                    .map(hit -> {
                        DriverLocationDocument doc = hit.getContent();

                        // 计算司机与乘客的实际距离（用于显示）
                        GeoPoint docLocation = doc.getLocation();
                        double calculatedDistance = calculateDistance(
                                form.getLatitude().doubleValue(), form.getLongitude().doubleValue(),
                                docLocation.getLat(), docLocation.getLon()
                        );
                        BigDecimal currentDistance = new BigDecimal(calculatedDistance).setScale(2, RoundingMode.UP);

                        // 创建返回对象（ES脚本已过滤，无需再次判断条件）
                        NearByDriverVo vo = new NearByDriverVo();
                        vo.setDriverId(doc.getDriverId());
                        vo.setDistance(currentDistance);
                        return vo;
                    })
                    .collect(Collectors.toList());

            log.info("ES脚本查询搜索附近司机成功，找到{}个完全符合条件的司机", result.size());
            return result;

        } catch (Exception e) {
            log.error("ES搜索附近司机失败", e);
            return new ArrayList<>();
        }
    }

    // 计算两点间距离（公里）
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // 地球半径（公里）
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
}

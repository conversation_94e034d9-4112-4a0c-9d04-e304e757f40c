package com.atguigu.daijia.map.service.impl;

import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.driver.client.DriverInfoFeignClient;
import com.atguigu.daijia.map.document.DriverLocationDocument;
import com.atguigu.daijia.map.repository.DriverLocationRepository;
import com.atguigu.daijia.model.entity.driver.DriverSet;
import com.atguigu.daijia.model.form.map.SearchNearByDriverForm;
import com.atguigu.daijia.model.form.map.UpdateDriverLocationForm;
import com.atguigu.daijia.model.vo.map.NearByDriverVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.geo.GeoPoint;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;

import org.springframework.data.geo.Distance;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LocationESServiceImpl {

    @Autowired
    private DriverLocationRepository driverLocationRepository;

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @Autowired
    private DriverInfoFeignClient driverInfoFeignClient;

    // ES版本：更新司机位置（使用UPSERT优化）
    public Boolean updateDriverLocationES(UpdateDriverLocationForm form) {
        try {
            // 远程调用获取司机个性化设置
            Result<DriverSet> driverSetResult = driverInfoFeignClient.getDriverSet(form.getDriverId());
            DriverSet driverSet = driverSetResult.getData();

            // 使用UPSERT操作避免先查询再更新
            DriverLocationDocument document = new DriverLocationDocument();
            document.setId(form.getDriverId().toString()); // 使用司机ID作为文档ID
            document.setDriverId(form.getDriverId());
            document.setLocation(new GeoPoint(form.getLatitude().doubleValue(), form.getLongitude().doubleValue()));
            document.setUpdateTime(new Date());
            document.setStatus(1);

            // 同步司机个性化设置到ES
            if (driverSet != null) {
                document.setAcceptDistance(driverSet.getAcceptDistance());
                document.setOrderDistance(driverSet.getOrderDistance());
            }

            // 直接保存，如果存在则更新，不存在则创建
            driverLocationRepository.save(document);
            log.info("ES UPSERT司机位置和设置成功，司机ID：{}", form.getDriverId());
            return true;
        } catch (Exception e) {
            log.error("ES更新司机位置失败，司机ID：{}", form.getDriverId(), e);
            return false;
        }
    }

    // ES版本：删除司机位置
    public Boolean removeDriverLocationES(Long driverId) {
        try {
            driverLocationRepository.deleteByDriverId(driverId);
            log.info("ES删除司机位置成功，司机ID：{}", driverId);
            return true;
        } catch (Exception e) {
            log.error("ES删除司机位置失败，司机ID：{}", driverId, e);
            return false;
        }
    }

    // ES版本：搜索附近司机（ES过滤地理位置和在线状态，Java层过滤司机个性化设置）
    public List<NearByDriverVo> searchNearByDriverES(SearchNearByDriverForm form) {
        try {
            // 使用Spring Data Elasticsearch进行查询优化

            // 使用Spring Data Elasticsearch的查询方式（简化版本）
            Point point = new Point(form.getLongitude().doubleValue(), form.getLatitude().doubleValue());
            Distance distance = new Distance(5, Metrics.KILOMETERS);

            // 构建基础查询条件：在线司机 + 地理位置范围，并按距离排序
            Criteria criteria = new Criteria("status").is(1)
                    .and(new Criteria("location").within(point, distance));

            Query searchQuery = new CriteriaQuery(criteria)
                    .addSort(Sort.by(Sort.Direction.ASC, "_geo_distance"));

            SearchHits<DriverLocationDocument> searchHits = elasticsearchOperations.search(searchQuery, DriverLocationDocument.class);

            // Java层进行司机个性化设置过滤
            List<NearByDriverVo> result = searchHits.stream()
                    .map(hit -> {
                        DriverLocationDocument doc = hit.getContent();

                        // 使用ES计算的距离，避免重复计算
                        BigDecimal currentDistance;
                        List<Object> sortValues = hit.getSortValues();
                        if (sortValues != null && !sortValues.isEmpty()) {
                            // ES返回的距离单位是米，转换为公里
                            double distanceInMeters = (Double) sortValues.get(0);
                            currentDistance = new BigDecimal(distanceInMeters / 1000.0).setScale(2, RoundingMode.UP);
                        } else {
                            // 降级方案：手动计算距离
                            GeoPoint docLocation = doc.getLocation();
                            double calculatedDistance = calculateDistance(
                                    form.getLatitude().doubleValue(), form.getLongitude().doubleValue(),
                                    docLocation.getLat(), docLocation.getLon()
                            );
                            currentDistance = new BigDecimal(calculatedDistance).setScale(2, RoundingMode.UP);
                        }

                        // 获取司机个性化设置
                        BigDecimal orderDistance = doc.getOrderDistance();
                        BigDecimal acceptDistance = doc.getAcceptDistance();

                        // 判断订单距离：司机设置为0或null表示无限制，否则必须大于等于预期里程
                        boolean orderDistanceValid = orderDistance == null || orderDistance.doubleValue() == 0 ||
                                orderDistance.subtract(form.getMileageDistance()).doubleValue() >= 0;

                        // 判断接单距离：司机设置为0或null表示无限制，否则必须大于等于当前距离
                        boolean acceptDistanceValid = acceptDistance == null || acceptDistance.doubleValue() == 0 ||
                                acceptDistance.subtract(currentDistance).doubleValue() >= 0;

                        // 如果两个条件都满足，创建并返回NearByDriverVo对象
                        if (orderDistanceValid && acceptDistanceValid) {
                            NearByDriverVo vo = new NearByDriverVo();
                            vo.setDriverId(doc.getDriverId());
                            vo.setDistance(currentDistance);
                            return vo;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull) // 过滤掉不满足条件的司机
                    .collect(Collectors.toList());

            log.info("ES搜索附近司机成功，ES找到{}个在线司机，Java层过滤后返回{}个符合条件的司机",
                    searchHits.getTotalHits(), result.size());
            return result;

        } catch (Exception e) {
            log.error("ES搜索附近司机失败", e);
            return new ArrayList<>();
        }
    }

    // 计算两点间距离（公里）
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // 地球半径（公里）
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
}

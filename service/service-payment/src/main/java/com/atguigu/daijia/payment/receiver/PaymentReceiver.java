package com.atguigu.daijia.payment.receiver;

import com.alibaba.fastjson.JSONObject;
import com.atguigu.daijia.common.constant.MqConst;
import com.atguigu.daijia.model.form.payment.ProfitsharingForm;
import com.atguigu.daijia.payment.service.WxPayService;
import com.atguigu.daijia.payment.service.WxProfitsharingService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @date 2025/6/26
 * @description 监听订单支付成功消息
 */
@Component
public class PaymentReceiver {

    @Autowired
    private WxPayService wxPayService;
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = MqConst.QUEUE_PAY_SUCCESS, durable = "true"),
            exchange = @Exchange(value = MqConst.EXCHANGE_ORDER),
            key = {MqConst.ROUTING_PAY_SUCCESS}
    ))
    public void paySuccess(String orderNo, Message message, Channel channel)  throws IOException{
        wxPayService.handleOrder(orderNo);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    @Autowired
    private WxProfitsharingService wxProfitsharingService;

    /**
     * 分账消息
     * @param param
     * @throws IOException
     */
    @RabbitListener(queues = MqConst.QUEUE_PROFITSHARING)
    public void profitsharingMessage(String param, Message message, Channel channel) throws IOException {
        try {
            // 1. 解析消息内容为分账表单对象
            ProfitsharingForm profitsharingForm = JSONObject.parseObject(param, ProfitsharingForm.class);

            // 2. 添加幂等性检查 - 查询是否已经分账过
            boolean alreadyProcessed = wxProfitsharingService.isOrderAlreadyProfitshared(profitsharingForm.getOrderNo());
            if (alreadyProcessed) {
                // 如果已经分账过，直接确认消息并返回
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 3. 调用微信分账服务执行分账
            wxProfitsharingService.profitsharing(profitsharingForm);

            // 4. 手动确认消息（第二个参数为multiple，表示是否批量确认）
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            e.printStackTrace(); // 记录异常信息
            try {
                // 5. 检查通道是否开放
                if (channel.isOpen()) {
                    // 处理失败，将消息重新放回队列（第三个参数为requeue）
                    // 修改为false，避免无限循环重试
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}

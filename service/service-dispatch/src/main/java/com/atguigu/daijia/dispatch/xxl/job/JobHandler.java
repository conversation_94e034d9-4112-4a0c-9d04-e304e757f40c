package com.atguigu.daijia.dispatch.xxl.job;

import com.atguigu.daijia.dispatch.mapper.XxlJobLogMapper;
import com.atguigu.daijia.dispatch.service.NewOrderService;
import com.atguigu.daijia.model.entity.dispatch.XxlJobLog;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @date 2025/6/14
 * @description 描述
 */
@Component
public class JobHandler {
    @Autowired
    private XxlJobLogMapper xxlJobLogMapper;

    @Autowired
    private NewOrderService newOrderService;
    @XxlJob("newOrderTaskHandler")
    public void newOrderTaskHandler(){
        // 记录任务调度日志
        XxlJobLog xxlJobLog=new XxlJobLog();
        xxlJobLog.setJobId(XxlJobHelper.getJobId()); // 当前任务id
        int startTime = (int) System.currentTimeMillis();
        try {
            // 执行任务：搜索附近代驾司机
            long jobId = XxlJobHelper.getJobId();
            newOrderService.executeTask(jobId);
            // 成功状态
            xxlJobLog.setStatus(1);
        } catch (Exception e) {
            // 失败状态
            xxlJobLog.setStatus(0);
            xxlJobLog.setError(e.getMessage());
            e.printStackTrace();
        } finally {
            int endTime = (int) System.currentTimeMillis();
            int  times = (int) (endTime - startTime);
            xxlJobLog.setTimes(times);
            xxlJobLogMapper.insert(xxlJobLog);
        }
    }
}

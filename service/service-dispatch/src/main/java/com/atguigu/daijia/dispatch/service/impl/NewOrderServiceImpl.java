package com.atguigu.daijia.dispatch.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.atguigu.daijia.common.constant.RedisConstant;
import com.atguigu.daijia.dispatch.mapper.OrderJobMapper;
import com.atguigu.daijia.dispatch.service.NewOrderService;
import com.atguigu.daijia.dispatch.xxl.client.XxlJobClient;
import com.atguigu.daijia.map.client.LocationFeignClient;
import com.atguigu.daijia.model.entity.dispatch.OrderJob;
import com.atguigu.daijia.model.enums.OrderStatus;
import com.atguigu.daijia.model.form.map.SearchNearByDriverForm;
import com.atguigu.daijia.model.vo.dispatch.NewOrderTaskVo;
import com.atguigu.daijia.model.vo.map.NearByDriverVo;
import com.atguigu.daijia.model.vo.order.NewOrderDataVo;
import com.atguigu.daijia.order.client.OrderInfoFeignClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class NewOrderServiceImpl implements NewOrderService {

    @Autowired
    private OrderJobMapper orderJobMapper;

    @Autowired
    private XxlJobClient xxlJobClient;

    @Autowired
    private LocationFeignClient locationFeignClient;

    @Autowired
    private OrderInfoFeignClient orderInfoFeignClient;

    @Autowired
    private RedisTemplate redisTemplate;
    // 添加并开始任务调度
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addAndStartTask(NewOrderTaskVo newOrderTaskVo) {
        //判断当前订单是否启动任务调度
        //根据id进行查询
        LambdaQueryWrapper<OrderJob> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderJob::getOrderId, newOrderTaskVo.getOrderId());
        OrderJob orderJob = orderJobMapper.selectOne(wrapper);
        //没有启动，进行操作
        if (orderJob == null) {
            // 创建并启动我们的任务调度
            //String executorHandler：执行 job的方法
            // String param：参数
            // String corn：crontab 表达式
            // String desc：任务描述
            Long jobId = xxlJobClient.addAndStart("newOrderTaskHandler",
                    "", "0 0/1 * * * ?", "新订单任务,订单id：" + newOrderTaskVo.getOrderId());
            // 保存任务id到order_job表中
            orderJob = new OrderJob();
            orderJob.setOrderId(newOrderTaskVo.getOrderId());
            orderJob.setJobId(jobId);
            orderJob.setParameter(JSONObject.toJSONString(newOrderTaskVo));
            orderJobMapper.insert(orderJob);
        }
        return orderJob.getJobId();
    }

    // 执行任务:  搜索附近代驾司机
    @Override
    public void executeTask(Long jobId) {
        // 添加重试机制
        OrderJob orderJob = null;
        int retryCount = 3; // 最大重试次数
        int retryDelayMs = 500;  // 初始重试延迟(毫秒)

        for (int i = 0; i < retryCount; i++) {
            orderJob = orderJobMapper.selectOne(
                    new LambdaQueryWrapper<OrderJob>().eq(OrderJob::getJobId, jobId));

            if (orderJob != null) {
                log.info("查询到任务数据, jobId: {}, orderId: {}", jobId, orderJob.getOrderId());
                break;
            }

            log.warn("第{}次查询任务数据失败, jobId: {}, 等待重试...", i+1, jobId);
            try {
                Thread.sleep(retryDelayMs);
                retryDelayMs *= 2; // 指数退避策略
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        if (null == orderJob) {
            log.warn("经过{}次重试后仍未找到任务数据, jobId: {}, 清理任务", retryCount, jobId);
            xxlJobClient.stopJob(jobId);
            xxlJobClient.removeJob(jobId);
            return;
        }
        // 根据jobId进行查询，判断当前任务是否创建
//        // 如果没有创建，不往下面执行
//        OrderJob orderJob = orderJobMapper.selectOne(
//                new LambdaQueryWrapper<OrderJob>().eq(OrderJob::getJobId, jobId));
//        if (null == orderJob) {
//            // 增强校验 可能会出现数据库中没有数据，但是任务已经创建没有被处理的情况 可能出现幽灵任务
//            xxlJobClient.stopJob(jobId);
//            xxlJobClient.removeJob(jobId);
//            return;
//        }
        //查询订单状态，如果订单状态不是待派单，不执行，是待派单，执行
        String josnString = orderJob.getParameter();
        NewOrderTaskVo newOrderTaskVo = JSON.parseObject(josnString, NewOrderTaskVo.class);
        Long orderId = newOrderTaskVo.getOrderId();
        Integer status = orderInfoFeignClient.getOrderStatus(orderId).getData();
        if (status.intValue() != OrderStatus.WAITING_ACCEPT.getStatus().intValue()) {
            // 停止任务调度
            xxlJobClient.stopJob(jobId);
            xxlJobClient.removeJob(jobId);
            orderJobMapper.delete(new LambdaQueryWrapper<OrderJob>().eq(OrderJob::getJobId, jobId));
            return;
        }
        //远程调用：搜索附近代驾司机
        //获取满足条件的代驾司机
        SearchNearByDriverForm searchNearByDriverForm = new SearchNearByDriverForm();
        searchNearByDriverForm.setLongitude(newOrderTaskVo.getStartPointLongitude());
        searchNearByDriverForm.setLatitude(newOrderTaskVo.getStartPointLatitude());
        searchNearByDriverForm.setMileageDistance(newOrderTaskVo.getExpectDistance());
        List<NearByDriverVo> nearByDriverVoList = locationFeignClient.searchNearByDriver(searchNearByDriverForm).getData();
        // 遍历司机列表，得到每个司机，为每个司机创建临时队列，存储新订单信息
        nearByDriverVoList.forEach(driver -> {
            //使用redis操作队列
            String repeatKey = RedisConstant.DRIVER_ORDER_REPEAT_LIST + newOrderTaskVo.getOrderId();
            //防止同一个订单重复推送给同一个司机  检查元素是否存在于集合中
            boolean isMember = redisTemplate.opsForSet().isMember(repeatKey, driver.getDriverId());
            // 如果不存在，添加到临时队列
            if (!isMember) {
                // 15分钟后过期
                // 添加元素到 Set
                Long added = redisTemplate.opsForSet().add(repeatKey, driver.getDriverId());
                // 如果是新创建的键，设置过期时间 15分钟
                if (added != null && added > 0) {
                    redisTemplate.expire(
                            repeatKey,
                            RedisConstant.DRIVER_ORDER_REPEAT_LIST_EXPIRES_TIME,
                            TimeUnit.MINUTES
                    );
                }
                NewOrderDataVo newOrderDataVo = new NewOrderDataVo();
                newOrderDataVo.setOrderId(newOrderTaskVo.getOrderId());
                newOrderDataVo.setStartLocation(newOrderTaskVo.getStartLocation());
                newOrderDataVo.setEndLocation(newOrderTaskVo.getEndLocation());
                newOrderDataVo.setExpectAmount(newOrderTaskVo.getExpectAmount());
                newOrderDataVo.setExpectDistance(newOrderTaskVo.getExpectDistance());
                newOrderDataVo.setExpectTime(newOrderTaskVo.getExpectTime());
                newOrderDataVo.setFavourFee(newOrderTaskVo.getFavourFee());
                newOrderDataVo.setDistance(driver.getDistance());
                newOrderDataVo.setCreateTime(newOrderTaskVo.getCreateTime());
                //将消息保存到司机的临时队列里面，司机接单了会定时轮询到他的临时队列获取订单消息
                String key = RedisConstant.DRIVER_ORDER_TEMP_LIST + driver.getDriverId();
                redisTemplate.opsForList().rightPush(key, JSONObject.toJSONString(newOrderDataVo));
                //过期时间：1分钟，1分钟未消费，自动过期
                //注：司机端开启接单，前端每5秒（远小于1分钟）拉取1次“司机临时队列”里面的新订单消息
                redisTemplate.expire(key, RedisConstant.DRIVER_ORDER_TEMP_LIST_EXPIRES_TIME, TimeUnit.MINUTES);
            }
        });
    }

    // 获取司机新订单队列数据
    @Override
    public List<NewOrderDataVo> findNewOrderQueueData(Long driverId) {
        List<NewOrderDataVo> list = new ArrayList<>();
        String key = RedisConstant.DRIVER_ORDER_TEMP_LIST + driverId;

        // 原子操作消费所有消息
        String content = null;
        while ((content = (String) redisTemplate.opsForList().leftPop(key)) != null) {
            list.add(JSON.parseObject(content, NewOrderDataVo.class));
        }
        return list;
    }

    // 清空司机新订单队列数据 司机接单前后都会进来清理
    @Override
    public Boolean clearNewOrderQueueData(Long driverId) {
        String key = RedisConstant.DRIVER_ORDER_TEMP_LIST + driverId;
        //直接删除，司机开启服务后，有新订单会自动创建容器
        redisTemplate.delete(key);
        return true;
    }

    // 停止并删除任务调度
    @Override
    public Boolean stopAndDeleteTask(Long orderId) {
        try {
            // 获取关联的job记录
            List<OrderJob> orderJobs = orderJobMapper.selectList(
                    new LambdaQueryWrapper<OrderJob>().eq(OrderJob::getOrderId, orderId)
            );

            if (CollectionUtils.isEmpty(orderJobs)) return false;

            // 提取jobIds
            List<Long> jobIds = orderJobs.stream()
                    .map(OrderJob::getJobId)
                    .collect(Collectors.toList());

            // 删除业务表记录
            orderJobMapper.delete(new LambdaQueryWrapper<OrderJob>().eq(OrderJob::getOrderId, orderId));

            // 清理调度中心任务
            jobIds.forEach(jobId -> {
                xxlJobClient.stopJob(jobId);    // 停止触发
                xxlJobClient.removeJob(jobId);  // 彻底删除
            });
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}

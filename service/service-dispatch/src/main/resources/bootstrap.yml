spring:
  application:
    name: service-dispatch
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.40.123:8848
        namespace: 6376d8c3-f3dc-41fe-8774-b34d854608a1
      config:
        server-addr: 192.168.40.123:8848
        namespace: 6376d8c3-f3dc-41fe-8774-b34d854608a1
        prefix: ${spring.application.name}
        file-extension: yaml
        shared-configs:
          - data-id: common-account.yaml

package com.atguigu.daijia.customer.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * @date 2025/5/29
 * @description 描述
 */
@Component
public class WxConfigOperator {
    @Autowired
    private WxConfigProperties wxConfigProperties;
    @Bean
    public WxMaService wxMaService(){
        // 微信小程序id和密钥
        WxMaDefaultConfigImpl wxMaDefaultConfig=new WxMaDefaultConfigImpl();
        wxMaDefaultConfig.setAppid(wxConfigProperties.getAppid());
        wxMaDefaultConfig.setSecret(wxConfigProperties.getSecret());

        WxMaService wxMaService=new WxMaServiceImpl();
        wxMaService.setWxMaConfig(wxMaDefaultConfig);
        return wxMaService;
    }
}

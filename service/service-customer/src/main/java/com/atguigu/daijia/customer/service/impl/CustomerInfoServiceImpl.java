package com.atguigu.daijia.customer.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.atguigu.daijia.common.execption.GuiguException;
import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.common.result.ResultCodeEnum;
import com.atguigu.daijia.common.util.WechatAESUtils;
import com.atguigu.daijia.customer.config.SmsConfigProperties;
import com.atguigu.daijia.customer.mapper.CustomerInfoMapper;
import com.atguigu.daijia.customer.mapper.CustomerLoginLogMapper;
import com.atguigu.daijia.customer.service.CustomerInfoService;
import com.atguigu.daijia.model.entity.customer.CustomerInfo;
import com.atguigu.daijia.model.entity.customer.CustomerLoginLog;
import com.atguigu.daijia.model.form.customer.CustomerLoginFrom;
import com.atguigu.daijia.model.form.customer.UpdateCustomerPhoneForm;
import com.atguigu.daijia.model.form.customer.UpdateWxPhoneForm;
import com.atguigu.daijia.model.vo.customer.CustomerLoginVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloopen.rest.sdk.BodyType;
import com.cloopen.rest.sdk.CCPRestSmsSDK;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class CustomerInfoServiceImpl extends ServiceImpl<CustomerInfoMapper, CustomerInfo> implements CustomerInfoService {

    @Autowired
    private WxMaService wxMaService;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;

    @Autowired
    private CustomerLoginLogMapper customerLoginLogMapper;

    /**
     * @return java.lang.Long
     * @Description 微信小程序登录接口
     * @Param [code]
     */
    @Override
    public Long login(CustomerLoginFrom customerLoginFrom) {
        // 1.获取code值，使用微信工具包，获取openid
        String openid = null;
        String sessionKey = null;
        WxMaJscode2SessionResult sessionInfo = null;
        try {
            sessionInfo =
                    wxMaService.getUserService().getSessionInfo(customerLoginFrom.getCode());
            openid = sessionInfo.getOpenid();
            sessionKey = sessionInfo.getSessionKey();
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        // 2.判断openid,是否第一次登入
        LambdaQueryWrapper<CustomerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInfo::getWxOpenId, openid);
        CustomerInfo customerInfo = customerInfoMapper.selectOne(wrapper);

        // 解密用户信息获取头像
        String avatarUrl = null;
        String nickname = null;

        log.info("=== 开始解密用户信息 ===");
        log.info("encryptedData是否为空: {}", org.apache.commons.lang.StringUtils.isBlank(customerLoginFrom.getEncryptedData()));
        log.info("iv是否为空: {}", org.apache.commons.lang.StringUtils.isBlank(customerLoginFrom.getIv()));
        log.info("sessionKey: {}", sessionKey);

        if (org.apache.commons.lang.StringUtils.isNotBlank(customerLoginFrom.getEncryptedData())
                && org.apache.commons.lang.StringUtils.isNotBlank(customerLoginFrom.getIv())) {

            // 先尝试官方工具包
            try {
                log.info("尝试使用微信官方工具包解密...");
                WxMaUserInfo userInfo = wxMaService.getUserService().getUserInfo(
                        sessionKey,
                        customerLoginFrom.getEncryptedData(),
                        customerLoginFrom.getIv()
                );
                avatarUrl = userInfo.getAvatarUrl();
                nickname = userInfo.getNickName();
                log.info("官方工具包解密成功!");
                log.info("avatarUrl: {}", avatarUrl);
                log.info("nickname: {}", nickname);
                log.info("gender: {}", userInfo.getGender());
                log.info("city: {}", userInfo.getCity());
                log.info("province: {}", userInfo.getProvince());
                log.info("country: {}", userInfo.getCountry());

                // 额外检查：打印用户信息的所有字段
                log.info("=== 微信用户信息详细内容 ===");
                log.info("unionId: {}", userInfo.getUnionId());
                log.info("language: {}", userInfo.getLanguage());
                log.info("watermark: {}", userInfo.getWatermark());
                log.info("================================");
            } catch (Exception e) {
                log.warn("官方工具包解密失败，尝试自定义AES解密", e);
                try {
                    log.info("尝试使用自定义AES解密...");
                    // 方案2：直接解析解密后的JSON
                    String decryptedData = WechatAESUtils.decrypt(customerLoginFrom.getEncryptedData(),
                            sessionKey,
                            customerLoginFrom.getIv());

                    log.info("解密后的完整JSON数据: {}", decryptedData);

                    // 直接使用JSON解析获取头像URL
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode root = objectMapper.readTree(decryptedData);

                    // 尝试多种可能的字段名
                    avatarUrl = root.path("avatarUrl").asText();
                    if (org.apache.commons.lang.StringUtils.isBlank(avatarUrl)) {
                        avatarUrl = root.path("avatar_url").asText();
                    }

                    nickname = root.path("nickName").asText();
                    if (org.apache.commons.lang.StringUtils.isBlank(nickname)) {
                        nickname = root.path("nickname").asText();
                    }
                    if (org.apache.commons.lang.StringUtils.isBlank(nickname)) {
                        nickname = root.path("nick_name").asText();
                    }

                    log.info("自定义AES解密结果:");
                    log.info("avatarUrl: {}", avatarUrl);
                    log.info("nickname: {}", nickname);

                    // 打印所有字段，帮助调试
                    log.info("JSON中的所有字段:");
                    root.fieldNames().forEachRemaining(fieldName -> {
                        log.info("  {}: {}", fieldName, root.path(fieldName).asText());
                    });

                } catch (Exception ex) {
                    log.error("自定义AES解密失败", ex);
                }
            }
        } else {
            log.info("encryptedData或iv为空，跳过解密");
        }

        // 尝试从明文用户信息中获取昵称（作为备用方案）
        if (customerLoginFrom.getUserInfo() != null &&
            (org.apache.commons.lang.StringUtils.isBlank(nickname) || "微信用户".equals(nickname))) {
            try {
                log.info("尝试从明文用户信息中获取昵称...");
                ObjectMapper objectMapper = new ObjectMapper();
                String userInfoJson = objectMapper.writeValueAsString(customerLoginFrom.getUserInfo());
                JsonNode userInfoNode = objectMapper.readTree(userInfoJson);

                String plainNickname = userInfoNode.path("nickName").asText();
                String plainAvatarUrl = userInfoNode.path("avatarUrl").asText();

                log.info("明文用户信息 - nickName: {}", plainNickname);
                log.info("明文用户信息 - avatarUrl: {}", plainAvatarUrl);

                // 如果明文昵称不是"微信用户"，则使用明文昵称
                if (org.apache.commons.lang.StringUtils.isNotBlank(plainNickname) &&
                    !"微信用户".equals(plainNickname)) {
                    nickname = plainNickname;
                    log.info("使用明文昵称: {}", nickname);
                }

                // 如果没有头像或头像是默认头像，尝试使用明文头像
                if (org.apache.commons.lang.StringUtils.isBlank(avatarUrl) &&
                    org.apache.commons.lang.StringUtils.isNotBlank(plainAvatarUrl)) {
                    avatarUrl = plainAvatarUrl;
                    log.info("使用明文头像: {}", avatarUrl);
                }

            } catch (Exception e) {
                log.warn("解析明文用户信息失败", e);
            }
        }

        log.info("=== 最终解密结果 ===");
        log.info("最终avatarUrl: {}", avatarUrl);
        log.info("最终nickname: {}", nickname);
        log.info("========================");
        // 3.第一次登录，添加数据
        if (customerInfo == null) {
            customerInfo = new CustomerInfo();

            // 设置昵称：优先使用解密得到的昵称
            if (org.apache.commons.lang.StringUtils.isNotBlank(nickname) && !"微信用户".equals(nickname)) {
                // 使用真实的微信昵称
                customerInfo.setNickname(nickname);
            }
            // 如果是默认的"微信用户"或解密失败，使用更友好的随机昵称
            else {
                customerInfo.setNickname("代驾用户_" + RandomStringUtils.randomAlphanumeric(6));
            }
            // 使用真实头像或默认头像
            customerInfo.setAvatarUrl(org.apache.commons.lang.StringUtils.isNotBlank(avatarUrl)
                    ? avatarUrl
                    : "https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg");
            // customerInfo.setAvatarUrl("https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg");
            customerInfo.setWxOpenId(openid);
            this.save(customerInfo);
        }
        // 老用户更新头像
        else if (org.apache.commons.lang.StringUtils.isNotBlank(avatarUrl)) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(avatarUrl)) {
                customerInfo.setAvatarUrl(avatarUrl);
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(nickname)) {
                customerInfo.setNickname(nickname);
            }
            customerInfoMapper.updateById(customerInfo);
        }

        // 4.记录登入日志信息
        CustomerLoginLog customerLoginLog = new CustomerLoginLog();
        customerLoginLog.setCustomerId(customerInfo.getId());
        customerLoginLog.setMsg("小程序登录");
        customerLoginLogMapper.insert(customerLoginLog);
        // 5.返回用户id
        return customerInfo.getId();
    }

    /**
     * @return com.atguigu.daijia.model.vo.customer.CustomerLoginVo
     * @Description 获取用户登录信息
     * @Param [customerId]
     */
    @Override
    public CustomerLoginVo getCustomerLoginInfo(Long customerId) {
        CustomerInfo customerInfo = this.getById(customerId);
        CustomerLoginVo customerInfoVo = new CustomerLoginVo();
        BeanUtils.copyProperties(customerInfo, customerInfoVo);
        //判断是否绑定手机号码，如果未绑定，小程序端发起绑定事件
        Boolean isBindPhone = StringUtils.hasText(customerInfo.getPhone());
        customerInfoVo.setIsBindPhone(isBindPhone);
        return customerInfoVo;
    }

    /**
     * 更新客户微信手机号码
     *
     * @return java.lang.Boolean
     * @Param [updateWxPhoneForm]
     */
    @Override
    public Boolean updateWxPhoneNumber(UpdateWxPhoneForm updateWxPhoneForm) {
        // 根据code值获取微信手机号码
        try {
            WxMaPhoneNumberInfo phoneNoInfo =
                    wxMaService.getUserService().getPhoneNoInfo(updateWxPhoneForm.getCode());
            String phoneNumber = phoneNoInfo.getPhoneNumber();
            // 更新用户信息
            Long customerId = updateWxPhoneForm.getCustomerId();
            CustomerInfo customerInfo = customerInfoMapper.selectById(customerId);
            customerInfo.setPhone(phoneNumber);
            customerInfoMapper.updateById(customerInfo);
            return true;
        } catch (WxErrorException e) {
            throw new GuiguException(ResultCodeEnum.DATA_ERROR);
        }
    }

    // 获取客户OpenId
    @Override
    public String getCustomerOpenId(Long customerId) {
        CustomerInfo customerInfo = this.getOne
                (new LambdaQueryWrapper<CustomerInfo>().eq(CustomerInfo::getId, customerId)
                        .select(CustomerInfo::getWxOpenId));
        return customerInfo.getWxOpenId();
    }

    @Autowired
    private SmsConfigProperties smsConfigProperties;

    @Autowired
    private RedisTemplate redisTemplate;

    // 发送验证码
    @Override
    public Result sendVerificationCode(String phone) {
        // 1. 校验手机号格式
        if (!Pattern.matches("^1[3-9]\\d{9}$", phone)) {
            return Result.fail("手机号格式不正确");
        }
        // 2. 生成随机验证码
        int code = ThreadLocalRandom.current().nextInt(1000, 9999);
        String codeStr = String.valueOf(code);
        log.info("--------------------------验证码: {}", codeStr);
        // 3. 发送短信
        CCPRestSmsSDK sdk = new CCPRestSmsSDK();
        sdk.init("app.cloopen.com", "8883");
        sdk.setAccount(smsConfigProperties.getAccountSId(), smsConfigProperties.getAccountToken());
        sdk.setAppId(smsConfigProperties.getAppId());
        sdk.setBodyType(BodyType.Type_JSON);
        String[] datas = {codeStr, "5"}; // 5分钟有效期
        HashMap<String, Object> result = sdk.sendTemplateSMS(phone, "1", datas);
        if (!"000000".equals(result.get("statusCode"))) {
            return Result.fail("短信发送失败");
        }
        // 4. 存储验证码到Redis (5分钟有效期)
        redisTemplate.opsForValue().set("SMS_CODE:" + phone, codeStr, 5, TimeUnit.MINUTES);
        return Result.ok();
    }

    // 手动绑定手机号码
    @Override
    public Boolean manualBindPhone(UpdateCustomerPhoneForm updateCustomerPhoneForm) {
        // 1. 校验验证码
        String cacheKey = "SMS_CODE:" + updateCustomerPhoneForm.getPhone();
        String correctCode = (String) redisTemplate.opsForValue().get(cacheKey);
        if (correctCode == null) {
            log.error("验证码已过期");
            return false;
        }
        if (!correctCode.equals(updateCustomerPhoneForm.getCode())) {
            log.error("验证码错误");
            return false;
        }
        // 2. 更新用户手机号
        CustomerInfo customerInfo = customerInfoMapper.selectById(updateCustomerPhoneForm.getCustomerId());
        customerInfo.setPhone(updateCustomerPhoneForm.getPhone());
        customerInfoMapper.updateById(customerInfo);
        // 3. 删除已使用的验证码
        redisTemplate.delete(cacheKey);
        return true;
    }
}

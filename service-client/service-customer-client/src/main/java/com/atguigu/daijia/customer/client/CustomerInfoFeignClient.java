package com.atguigu.daijia.customer.client;

import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.model.form.customer.CustomerLoginFrom;
import com.atguigu.daijia.model.form.customer.UpdateCustomerPhoneForm;
import com.atguigu.daijia.model.form.customer.UpdateWxPhoneForm;
import com.atguigu.daijia.model.vo.customer.CustomerLoginVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "service-customer")
public interface CustomerInfoFeignClient {
    /**
     * @return com.atguigu.daijia.common.result.Result<java.lang.Long>
     * @Description 微信小程序登录接口
     */
    @PostMapping("/customer/info/login")
    public Result<Long> login(@RequestBody CustomerLoginFrom customerLoginFrom);

    /**
     * 获取客户登录信息
     *
     * @param customerId
     * @return
     */
    @GetMapping("/customer/info/getCustomerLoginInfo/{customerId}")
    Result<CustomerLoginVo> getCustomerLoginInfo(@PathVariable("customerId") Long customerId);

    @PostMapping("/customer/info/updateWxPhoneNumber")
    public Result<Boolean> updateWxPhoneNumber(@RequestBody UpdateWxPhoneForm updateWxPhoneForm);

    /**
     * 获取客户OpenId
     *
     * @param customerId
     * @return
     */
    @GetMapping("/customer/info/getCustomerOpenId/{customerId}")
    Result<String> getCustomerOpenId(@PathVariable("customerId") Long customerId);

    /**
     * 发送验证码
     *
     * @return com.atguigu.daijia.common.result.Result
     * @Param [phone]
     */
    @GetMapping("/customer/info/sendVerificationCode/{phone}")
    public Result sendVerificationCode(@PathVariable("phone") String phone);

    @PostMapping("/customer/info/manualBindPhone")
    public Result<Boolean> manualBindPhone(@RequestBody UpdateCustomerPhoneForm updateCustomerPhoneForm);
}

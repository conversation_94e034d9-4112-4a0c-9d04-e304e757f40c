package com.atguigu.daijia.system.client;

import com.atguigu.daijia.common.result.Result;
import com.atguigu.daijia.model.entity.system.SysMenu;
import com.atguigu.daijia.model.vo.system.AssginMenuVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(value = "service-system")
public interface SysMenuFeignClient {

    /**
     * 获取用户菜单列表
     *
     * @return
     */
    @GetMapping("/sysMenu/getUserMenuList")
    Result<Map<String, Object>> getUserMenuList();

    /**
     * 获取菜单
     *
     * @return
     */
    @GetMapping("/sysMenu/findNodes")
    Result<List<SysMenu>> findNodes();

    /**
     * 根据ID获取菜单
     *
     * @param id
     * @return
     */
    @GetMapping("/sysMenu/getById/{id}")
    Result<SysMenu> getById(@PathVariable("id") Long id);

    @PostMapping("/sysMenu/save")
    Result<Boolean> save(@RequestBody SysMenu sysMenu);

    @PutMapping("/sysMenu/update")
    Result<Boolean> update(@RequestBody SysMenu permission);

    @DeleteMapping("/sysMenu/remove/{id}")
    Result<Boolean> remove(@PathVariable Long id);

    /**
     * 根据角色获取菜单
     *
     * @param roleId
     * @return
     */
    @GetMapping("/sysMenu/toAssign/{roleId}")
    Result<List<SysMenu>> toAssign(@PathVariable Long roleId);

    /**
     * 给角色分配权限
     *
     * @param assginMenuVo
     * @return
     */
    @PostMapping("/sysMenu/doAssign")
    Result<Boolean> doAssign(@RequestBody AssginMenuVo assginMenuVo);
}

